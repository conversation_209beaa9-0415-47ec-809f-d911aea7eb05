import { Injectable, Logger, NotFoundException, ConflictException, BadRequestException } from "@nestjs/common";
import { Prisma, CustomValueType } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import { CreateCustomValueDto, UpdateCustomValueDto } from "./dto/custom-value.dto";

@Injectable()
export class CustomValueService {
  private readonly logger = new Logger(CustomValueService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateCustomValueDto, userId: string) {
    try {
      // Validate value based on type
      this.validateValue(data.value, data.type);

      return await this.prisma.customValue.create({
        data: {
          ...data,
          createdBy: userId,
          lastUpdatedBy: userId
        }
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === "P2002") {
          throw new ConflictException(`Custom value with key '${data.key}' already exists`);
        }
      }
      throw error;
    }
  }

  async update(key: string, data: UpdateCustomValueDto, userId: string) {
    // First check if value exists and if it's a system value
    const existing = await this.prisma.customValue.findUnique({ where: { key } });
    if (!existing) {
      throw new NotFoundException(`Custom value with key '${key}' not found`);
    }

    // If type is changing, validate the value against the new type
    const type = data.type || existing.type;
    if (data.value) {
      this.validateValue(data.value, type);
    }

    return this.prisma.customValue.update({
      where: { key },
      data: {
        ...data,
        lastUpdatedBy: userId
      }
    });
  }

  async delete(key: string) {
    const value = await this.prisma.customValue.findUnique({ where: { key } });
    if (!value) {
      throw new NotFoundException(`Custom value with key '${key}' not found`);
    }

    if (value.isSystem) {
      throw new BadRequestException(`Cannot delete system value '${key}'`);
    }

    return this.prisma.customValue.delete({ where: { key } });
  }

  async findAll(category?: string) {
    const where = category ? { category } : undefined;
    return this.prisma.customValue.findMany({ where });
  }

  async findByKey(key: string) {
    const value = await this.prisma.customValue.findUnique({ where: { key } });
    if (!value) {
      throw new NotFoundException(`Custom value with key '${key}' not found`);
    }
    return value;
  }

  private validateValue(value: string, type: CustomValueType): void {
    try {
      switch (type) {
        case CustomValueType.NUMBER:
          const num = Number(value);
          if (isNaN(num)) {
            throw new Error("Invalid number");
          }
          break;

        case CustomValueType.BOOLEAN:
          if (value !== "true" && value !== "false") {
            throw new Error("Invalid boolean value");
          }
          break;

        case CustomValueType.JSON:
          JSON.parse(value);
          break;

        case CustomValueType.STRING:
          // No validation needed
          break;

        default:
          throw new Error(`Unsupported type: ${type}`);
      }
    } catch (error) {
      throw new BadRequestException(`Invalid value for type ${type}: ${error.message}`);
    }
  }

  // Helper methods to get parsed values
  async getNumber(key: string): Promise<number> {
    const value = await this.findByKey(key);
    if (value.type !== CustomValueType.NUMBER) {
      throw new BadRequestException(`Custom value '${key}' is not a number`);
    }
    return Number(value.value);
  }

  async getBoolean(key: string): Promise<boolean> {
    const value = await this.findByKey(key);
    if (value.type !== CustomValueType.BOOLEAN) {
      throw new BadRequestException(`Custom value '${key}' is not a boolean`);
    }
    return value.value === "true";
  }

  async getJson<T>(key: string): Promise<T> {
    const value = await this.findByKey(key);
    if (value.type !== CustomValueType.JSON) {
      throw new BadRequestException(`Custom value '${key}' is not JSON`);
    }
    return JSON.parse(value.value) as T;
  }

  async getString(key: string): Promise<string> {
    const value = await this.findByKey(key);
    if (value.type !== CustomValueType.STRING) {
      throw new BadRequestException(`Custom value '${key}' is not a string`);
    }
    return value.value;
  }
}
