import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { CategoryService } from './category.service';
import { Category } from './category.model';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Resolver(() => Category)
export class CategoryResolver {
  constructor(private readonly categoryService: CategoryService) {}

  @Query(() => [Category], { name: 'categories' })
  async findAll() {
    return this.categoryService.findMany({});
  }

  @Query(() => Category, { name: 'category' })
  async findOne(@Args('id', { type: () => ID }) id: string) {
    return this.categoryService.findUnique({ id });
  }

  @Mutation(() => Category)
  async createCategory(@Args('data') data: CreateCategoryDto) {
    return this.categoryService.create(data);
  }

  @Mutation(() => Category)
  async updateCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('data') data: UpdateCategoryDto,
  ) {
    return this.categoryService.update(id, data);
  }

  @Mutation(() => Category)
  async deleteCategory(@Args('id', { type: () => ID }) id: string) {
    return this.categoryService.delete(id);
  }
}
