import { ObjectType, Field, ID } from '@nestjs/graphql';
import { TicketPriority } from '@prisma/client';
import { Category } from '../category/category.model';
import { Comment } from '../comment/comment.model';
import { SupportFile } from '../backblaze/support-file.model';

@ObjectType('Ticket')
export class TicketModel {
  @Field(() => ID)
  id: string;

  @Field()
  subject: string;

  @Field()
  description: string;

  @Field()
  status: string;

  @Field(() => TicketPriority)
  priority: TicketPriority;

  @Field({ nullable: true })
  accountId?: string;

  @Field({ nullable: true })
  partnerId?: string;

  @Field(() => [String], { nullable: true })
  assignedTo?: string[];

  @Field()
  createdBy: string;

  @Field()
  lastUpdatedBy: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field(() => Category)
  category: Category;

  @Field(() => [Comment], { nullable: true })
  comments?: Comment[];

  @Field(() => [SupportFile], { nullable: true })
  files?: SupportFile[];
}
