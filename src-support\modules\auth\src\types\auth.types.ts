export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;

  // Additional properties for compatibility with existing code
  phone?: string;
  password?: string;
  country?: string;
  verifiedEmail?: boolean;
  verifiedPhone?: boolean;
  totpSecret?: string;
  partnerId?: string;
  mfaEnabled?: boolean;
  active?: boolean;
  accountId?: string;
  isAdmin?: boolean;

  // External auth service fields
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface JWTPayload {
  sub: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: string[];
  iat: number;
  exp: number;
  iss: string;
  aud: string;

  // External auth service fields
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface AuthConfig {
  authJwksUrl: string;
  encryptionKey: string;
  cookieNames: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface DecryptedTokens {
  accessToken: string;
  refreshToken?: string;
}

export interface JWKSResponse {
  keys: Array<{
    kty: string;
    e: string;
    n: string;
    kid: string;
  }>;
}

export interface AuthenticatedRequest extends Request {
  user: User;
}

// Extend Express Request type globally
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}
