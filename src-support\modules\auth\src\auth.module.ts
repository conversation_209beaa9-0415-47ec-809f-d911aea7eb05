import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '../../../config/config.module';
import { AuthSharedService } from './auth.service';
import { AuthSharedGuard } from './auth.guard';
import { AuthSharedMiddleware } from './auth.middleware';
import { AuthSharedController } from './auth.controller';
import { ApiGuard, AdminGuard, GraphQLAuthGuard } from './guards';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard, GraphQLAuthGuard],
  controllers: [AuthSharedController],
  exports: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard, GraphQLAuthGuard],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthSharedMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}

// Export everything for easy importing
export * from './auth.service';
export { AuthSharedGuard, Roles, Permissions, Public } from './auth.guard';
export * from './auth.middleware';
export * from './auth.controller';
export * from './guards';
export { getCurrentUser } from './decorators/user.decorator';
export * from './types/auth.types';
export * from './types';
