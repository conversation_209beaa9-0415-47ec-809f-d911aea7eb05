import {
  Controller,
  Post,
  Get,
  Delete,
  UseGuards,
  Request,
  Body,
  Query,
  Param,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Req
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiQuery,
  ApiBody
} from "@nestjs/swagger";
import { SupportFile } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { AuthenticatedRequest } from "../auth/src/types";
import { multerConfig } from "./config/multer.config";
import { FileUploadDto, FileResponseDto } from "./dto/file-upload.dto";
import { FileService } from "./file.service";
import { FastifyRequest } from "fastify";

// Define a type for Fastify file upload
export interface FastifyFileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

@ApiTags("Files")
@Controller("files")
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Post("upload")
  @ApiOperation({ summary: "Upload a file" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        },
        ticketId: { type: "string" },
        commentId: { type: "string" },
      },
      required: ["file"],
    },
  })
  @ApiResponse({ status: 201, description: "File uploaded successfully", type: FileResponseDto })
  async uploadFile(
    @Req() req: FastifyRequest
  ): Promise<SupportFile> {
    // Use Fastify's file() method to get the uploaded file
    const fileObj = await (req as any).file();
    if (!fileObj) {
      throw new BadRequestException("File is required");
    }
    // ticketId and commentId are available in fileObj.fields
    const { ticketId, commentId } = fileObj.fields || {};
    const buffer = await fileObj.toBuffer();
    const fileForService = {
      fieldname: fileObj.fieldname,
      originalname: fileObj.filename,
      encoding: fileObj.encoding,
      mimetype: fileObj.mimetype,
      buffer,
      size: buffer.length,
    };
    // Get user from req.user (if using guards that attach user)
    const user = (req as any).user;
    if (!user || !user.id) {
      throw new BadRequestException("User not found in request");
    }
    try {
      return await this.fileService.uploadFile(
        fileForService,
        user.id,
        ticketId?.value ?? ticketId,
        commentId?.value ?? commentId
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while uploading the file");
    }
  }

  @Get()
  @ApiOperation({ summary: "Get files for a ticket or comment" })
  @ApiQuery({ name: "ticketId", required: false })
  @ApiQuery({ name: "commentId", required: false })
  @ApiResponse({ status: 200, description: "List of files", type: [FileResponseDto] })
  async getFiles(
    @Query("ticketId") ticketId?: string,
    @Query("commentId") commentId?: string,
  ): Promise<SupportFile[]> {
    try {
      return await this.fileService.getFiles(ticketId, commentId);
    } catch (error) {
      throw new InternalServerErrorException("An error occurred while fetching files");
    }
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a file by id" })
  @ApiResponse({ status: 200, description: "File found", type: FileResponseDto })
  @ApiResponse({ status: 404, description: "File not found" })
  async getFile(@Param("id") id: string): Promise<SupportFile> {
    try {
      const file = await this.fileService.findById(id);
      if (!file) {
        throw new NotFoundException("File not found");
      }
      return file;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while fetching the file");
    }
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a file" })
  @ApiResponse({ status: 200, description: "File deleted successfully" })
  @ApiResponse({ status: 404, description: "File not found" })
  async deleteFile(@Param("id") id: string): Promise<SupportFile> {
    try {
      return await this.fileService.deleteFile(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while deleting the file");
    }
  }
}

