import { ObjectType, Field, ID } from '@nestjs/graphql';
import { SubCategoryEntity } from '../../subcategory/entities/subcategory.entity';
import { ProductEntity } from '../../product/entities/product.entity';
import { MerchantEntity } from '../../merchant/entities/merchant.entity';

@ObjectType()
export class CategoryEntity {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field()
  merchantId: string;

  // Relations
  @Field(() => [SubCategoryEntity])
  subCategories: SubCategoryEntity[];

  @Field(() => [ProductEntity])
  products: ProductEntity[];

  @Field(() => MerchantEntity)
  merchant: MerchantEntity;
}
