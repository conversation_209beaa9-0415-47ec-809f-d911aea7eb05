import { IsString, IsOptional, IsBoolean, IsInt, IsArray, IsEnum } from 'class-validator';
import { InputType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { ProductStatus, SaleType } from '@prisma/client';

// Register enums for GraphQL
registerEnumType(ProductStatus, {
  name: 'ProductStatus',
  description: 'The status of the product (ACTIVE, INACTIVE, PENDING, DISCONTINUED)',
});

registerEnumType(SaleType, {
  name: 'SaleType',
  description: 'The sale type of the product (CASH, SUBSCRIPTION, INSTALLMENT)',
});

@InputType()
export class CreateProductDto {
  @Field()
  @ApiProperty({
    description: 'Product name',
    example: 'Premium Wireless Headphones',
    minLength: 1,
    maxLength: 255
  })
  @IsString()
  name!: string;

  @Field(() => Int, { nullable: true })
  @ApiProperty({
    description: 'Product price in cents (e.g., 2999 for $29.99)',
    example: 2999,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  price?: number;

  @Field()
  @ApiProperty({
    description: 'Stock Keeping Unit - unique product identifier',
    example: 'PWH-001-BLK',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  sku!: string;

  @Field()
  @ApiProperty({
    description: 'Category ID that this product belongs to',
    example: 'cat_electronics_001',
    format: 'uuid'
  })
  @IsString()
  categoryId!: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Subcategory ID for more specific categorization',
    example: 'cmfl41swn000erv01h9963wi4',
    format: 'uuid',
    required: false
  })
  @IsOptional()
  @IsString()
  subCategoryId?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Product brand name',
    example: 'TechSound Pro',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  brand?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Product weight with unit (e.g., "250g", "1.2kg")',
    example: '250g',
    maxLength: 50,
    required: false
  })
  @IsOptional()
  @IsString()
  itemWeight?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Product length with unit (e.g., "15cm", "6 inches")',
    example: '15cm',
    maxLength: 50,
    required: false
  })
  @IsOptional()
  @IsString()
  length?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Product width with unit (e.g., "10cm", "4 inches")',
    example: '10cm',
    maxLength: 50,
    required: false
  })
  @IsOptional()
  @IsString()
  width?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Detailed product description',
    example: 'High-quality wireless headphones with noise cancellation, 30-hour battery life, and premium sound quality.',
    maxLength: 2000,
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Whether the product is available in physical stores',
    example: true,
    default: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isInStore?: boolean;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Whether the product is available for online purchase',
    example: true,
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isOnline?: boolean;

  @Field(() => [String], { nullable: true })
  @ApiProperty({
    description: 'Array of product image URLs',
    example: [
      'https://example.com/images/product1-front.jpg',
      'https://example.com/images/product1-side.jpg',
      'https://example.com/images/product1-back.jpg'
    ],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  productImages?: string[];

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Creation timestamp (ISO 8601 format) - usually auto-generated',
    example: '2024-01-15T10:30:00.000Z',
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @Field({ nullable: true })
  @ApiProperty({
    description: 'Deletion timestamp (ISO 8601 format) - for soft deletes',
    example: null,
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsString()
  deletedAt?: string;

  @Field()
  @ApiProperty({
    description: 'Merchant ID that owns this product',
    example: 'merchant_12345',
    format: 'uuid'
  })
  @IsString()
  merchantId!: string;

  @Field(() => ProductStatus, { nullable: true })
  @ApiProperty({
    description: 'Current status of the product',
    enum: ProductStatus,
    example: ProductStatus.ACTIVE,
    default: ProductStatus.PENDING,
    required: false
  })
  @IsOptional()
  @IsEnum(ProductStatus)
  productStatus?: ProductStatus;

  @Field(() => Int, { nullable: true })
  @ApiProperty({
    description: 'Available stock count',
    example: 150,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  count?: number;

  @Field(() => [String], { nullable: true })
  @ApiProperty({
    description: 'Array of discount IDs applied to this product',
    example: ['discount_summer2024', 'discount_bulk10'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  discountIds?: string[];

  @Field(() => SaleType, { nullable: true })
  @ApiProperty({
    description: 'Type of sale for this product',
    enum: SaleType,
    example: SaleType.CASH,
    default: SaleType.CASH,
    required: false
  })
  @IsOptional()
  @IsEnum(SaleType)
  saleType?: SaleType;
}