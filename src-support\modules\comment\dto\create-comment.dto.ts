import { InputType, Field } from "@nestjs/graphql";
import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsUUID } from "class-validator";

@InputType()
export class CreateCommentDto {
  @ApiProperty({ description: "Ticket ID" })
  @IsUUID()
  @Field()
  ticketId: string;
  @ApiProperty({ 
    description: "Comment message",
    example: "This is a comment on the ticket"
  })
  @IsString()
  @Field()
  message: string;
}
