import { ObjectType, Field } from '@nestjs/graphql';
import { SupportFile } from '../backblaze/support-file.model';

@ObjectType()
export class Comment {
  @Field(() => String)
  id: string;

  @Field()
  message: string;

  @Field()
  authorId: string;

  @Field()
  ticketId: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field(() => [SupportFile], { nullable: true })
  files?: SupportFile[];
}
