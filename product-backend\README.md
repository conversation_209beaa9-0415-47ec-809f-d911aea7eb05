# NestJS + Fastify Starter

A boilerplate for initializing a NestJS project and configuring it to use **Fastify** instead of the default Express adapter.

---

## 🚀 Initialization

1. **Create a new NestJS project**

   ```bash
   npx @nestjs/cli new my-fastify-app
   ```

   *(or use `nest new my-fastify-app` if you have the CLI globally installed)*

2. **Enter the project folder**

   ```bash
   cd my-fastify-app
   ```

---

## ⚡ Switch to Fastify

1. **Install Fastify and the NestJS Fastify adapter**

   ```bash
   npm install fastify @nestjs/platform-fastify
   ```

2. **Update `main.ts`**
   Replace the default Express setup with Fastify:

   ```ts
   import { NestFactory } from '@nestjs/core';
   import { AppModule } from './app.module';
   import {
     FastifyAdapter,
     NestFastifyApplication,
   } from '@nestjs/platform-fastify';

   async function bootstrap() {
     const app = await NestFactory.create<NestFastifyApplication>(
       AppModule,
       new FastifyAdapter(),
     );
     await app.listen(3000);
   }
   bootstrap();
   ```

3. **Run the app**

   ```bash
   npm run start:dev
   ```

   Your app will now be running on [http://localhost:3000](http://localhost:3000) with **Fastify** 🚀.

---

## 📂 Project Structure

```
my-fastify-app/
├── src/
│   ├── app.controller.ts
│   ├── app.module.ts
│   ├── app.service.ts
│   └── main.ts  <-- Fastify bootstrap code
├── test/
├── package.json
└── tsconfig.json
```

---

## 🛠 Useful Commands

* **Development**

  ```bash
  npm run start:dev
  ```
* **Production build**

  ```bash
  npm run build
  npm run start:prod
  ```
* **Testing**

  ```bash
  npm run test
  ```

---

## ✅ Notes

* NestJS works the same way with Fastify as with Express — decorators, modules, and providers don’t change.
* You can configure Fastify plugins (e.g., CORS, compression, etc.) directly in the `main.ts`.

# product