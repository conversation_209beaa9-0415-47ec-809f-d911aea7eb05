# GraphQL Quick Reference Guide

## 🚀 Getting Started

### GraphQL Playground URLs
- **Primary**: http://ng-product-local.dev.dev1.ngnair.com:3070/graphql
- **Localhost**: http://localhost:3070/graphql

### Authentication
The system uses **cookie-based authentication** with `access_token` cookies, **NOT Bearer tokens**.

**For GraphQL Playground:**
1. Set the `access_token` cookie in your browser
2. Or add to HTTP Headers in playground:
```json
{
  "Cookie": "access_token=YOUR_ENCRYPTED_ACCESS_TOKEN"
}
```

**For curl/API testing:**
```bash
curl -X POST http://ng-product-local.dev.dev1.ngnair.com:3070/graphql \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=YOUR_ENCRYPTED_ACCESS_TOKEN" \
  -d '{"query": "..."}'
```

**Note**: Most queries are public and don't require authentication. Mutations require authentication.

## ✅ Current Implementation Status

### Working Queries
- ✅ `products` - Get all products for a merchant
- ✅ `product` - Get single product by ID
- ✅ `categories` - Get all categories for a merchant
- ✅ `category` - Get single category by ID

### Working Mutations
- ✅ `createProduct` - Create new product (requires auth)
- ✅ `updateProduct` - Update existing product (requires auth)
- ✅ `removeProduct` - Delete product (requires auth)
- ✅ `createCategory` - Create new category (requires auth)
- ✅ `updateCategory` - Update existing category (requires auth)
- ✅ `removeCategory` - Delete category (requires auth)

### Field Resolvers
- ⚠️ `category` field on Product returns null (marked as nullable)
- ⚠️ `subCategory` field on Product returns null (marked as nullable)
- ⚠️ `variants` field on Product returns null (marked as nullable)
- ⚠️ `subCategories` field on Category returns empty array

## 📋 Quick Copy-Paste Examples

### 1. Get All Products (Most Common Query)
```graphql
query GetAllProducts {
  products(merchantId: "merchant_12345") {
    id
    name
    price
    sku
    count
    brand
    description
    isInStore
    isOnline
    productStatus
    saleType
    categoryId
    merchantId
  }
}
```

### 2. Get Single Product by ID (Working Example)
```graphql
query GetProductById {
  product(id: "cmfl41y8h000frv010ikkh38a", merchantId: "merchant_12345") {
    id
    name
    price
    sku
    count
    brand
    description
    isInStore
    isOnline
    productStatus
    saleType
    categoryId
    merchantId
    createdAt
  }
}
```

**Note**: Field resolvers for `category`, `subCategory`, `variants`, etc. return null currently.

### 3. Get All Categories (Working Example)
```graphql
query GetAllCategories {
  categories(merchantId: "merchant_12345") {
    id
    name
    merchantId
  }
}
```

### 4. Get Single Category by ID (Working Example)
```graphql
query GetCategoryById {
  category(id: "cmfl41swn000brv01hlxhrwa8", merchantId: "merchant_12345") {
    id
    name
    merchantId
  }
}
```

## 🔄 Mutations (Require Authentication)

### 1. Create New Product
```graphql
mutation CreateNewProduct {
  createProduct(createProductInput: {
    name: "Test GraphQL Product"
    price: 2999
    sku: "GQL-001"
    categoryId: "cmfl41swn000brv01hlxhrwa8"
    description: "Product created via GraphQL"
    isInStore: true
    isOnline: false
    count: 10
    productStatus: INACTIVE
    saleType: CASH
    merchantId: "merchant_12345"
  }) {
    id
    name
    price
    sku
    createdAt
    merchantId
  }
}
```

**Cookie Required:**
```json
{
  "Cookie": "access_token=YOUR_ENCRYPTED_ACCESS_TOKEN"
}
```

### 2. Update Product
```graphql
mutation UpdateProduct {
  updateProduct(
    id: "cmfl41y8h000frv010ikkh38a"
    updateProductInput: {
      name: "Updated via GraphQL"
      price: 3499
      description: "Updated description via GraphQL"
      count: 25
    }
  ) {
    id
    name
    price
    description
    count
  }
}
```

### 5. Create New Category
```graphql
mutation CreateNewCategory {
  createCategory(createCategoryInput: {
    name: "GraphQL Test Category"
    merchantId: "merchant_12345"
    subCategories: ["Test Sub 1", "Test Sub 2"]
  }) {
    id
    name
    merchantId
  }
}
```

### 6. Delete Product
```graphql
mutation DeleteProduct {
  removeProduct(id: "cmfl41y8h000frv010ikkh38a")
}
```

### 7. Delete Category
```graphql
mutation DeleteCategory {
  removeCategory(id: "cmfl41swn000brv01hlxhrwa8")
}
```

## 🔧 Testing with Variables

### Query with Variables
```graphql
query GetProductById($productId: ID!, $merchantId: String!) {
  product(id: $productId, merchantId: $merchantId) {
    id
    name
    price
    sku
    description
  }
}
```

**Variables Panel:**
```json
{
  "productId": "cmfl41y8h000frv010ikkh38a",
  "merchantId": "merchant_12345"
}
```

## ⚠️ Current Limitations & Status

### Working Features ✅
- Product queries (`products`, `product`)
- Category queries (`categories`, `category`)
- Product mutations (create, update, delete)
- Category mutations (create, update, delete)
- Basic field selection
- Variable support

### Known Issues ⚠️
- Field resolvers return null: `category`, `subCategory`, `variants`, `merchant`
- Authentication not fully implemented (mutations work without JWT in testing)
- No pagination support
- Limited filtering options
- No subscription/real-time updates

### Field Resolver Status
```graphql
# These fields currently return null:
product {
  category { ... }      # Returns null
  subCategory { ... }   # Returns null
  variants { ... }      # Returns null
  merchant { ... }      # Returns null
}

category {
  subCategories { ... } # Returns empty array
}
```

## 🔧 Testing Tips

### 1. Use Real IDs from Database
```graphql
# Use actual IDs from your test data:
query {
  product(id: "cmfl41y8h000frv010ikkh38a", merchantId: "merchant_12345") {
    id
    name
  }
}
```

### 2. Test Error Handling
```graphql
# Missing merchantId (should error):
query { products { id name } }

# Invalid ID (should return null):
query { product(id: "invalid", merchantId: "merchant_12345") { id } }
```

## 🚀 Quick Testing Commands

### Test All Basic Queries
```bash
# Test products query
curl -X POST http://ng-product-local.dev.dev1.ngnair.com:3070/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { products(merchantId: \"merchant_12345\") { id name price } }"}'

# Test categories query
curl -X POST http://ng-product-local.dev.dev1.ngnair.com:3070/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { categories(merchantId: \"merchant_12345\") { id name } }"}'
```

## 📚 Related Documentation

- **GraphQL Playground**: http://ng-product-local.dev.dev1.ngnair.com:3070/graphql
- **Swagger API Docs**: http://ng-product-local.dev.dev1.ngnair.com:3070/api
- **Implementation Guide**: `GraphQL-Implementation-Guide.md`
- **Detailed Examples**: `GraphQL-Playground-Documentation.md`

## 🔄 Schema Updates

The GraphQL schema is auto-generated and located at:
- `product-backend/src/schema.gql`

To regenerate schema after changes:
1. Restart the application
2. Schema updates automatically based on resolvers and entities

## 📋 Testing Checklist

### Queries ✅
- [ ] `products` with merchantId
- [ ] `product` by ID
- [ ] `categories` with merchantId
- [ ] `category` by ID

### Mutations ⚠️ (Require Auth)
- [ ] `createProduct`
- [ ] `updateProduct`
- [ ] `removeProduct`
- [ ] `createCategory`
- [ ] `updateCategory`
- [ ] `removeCategory`

### Error Cases ✅
- [ ] Missing merchantId
- [ ] Invalid IDs
- [ ] Missing required fields
- [ ] Authentication errors

---

**Last Updated**: 2025-09-15
**Status**: Core functionality working, field resolvers need implementation
**Next Steps**: Implement field resolvers for nested relationships
