# Complete GraphQL API Documentation

## 🚀 Quick Start

Access the GraphQL Playground at: **http://ng-product-local.dev.dev1.ngnair.com:3070/graphql**

## 🔐 Authentication

For mutations and protected queries, include the Authorization header:
```
{
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```

## 📊 **Implementation Status**

| Module | REST API | GraphQL Entity | GraphQL Resolver | Coverage |
|--------|----------|----------------|------------------|----------|
| Product | ✅ | ✅ | ✅ | **100%** |
| Category | ✅ | ✅ | ✅ | **100%** |
| SubCategory | ✅ | ✅ | ✅ | **100%** |
| Variant | ✅ | ✅ | ✅ | **100%** |
| Merchant | ✅ | ✅ | ✅ | **100%** |
| Discount | ✅ | ✅ | ✅ | **100%** |
| Subscription Plan | ✅ | ✅ | ✅ | **100%** |
| Payment Plan | ✅ | ✅ | ✅ | **100%** |
| Transaction Log | ✅ | ✅ | ✅ | **100%** |

**Overall GraphQL Coverage**: **100%** (9 out of 9 modules)

## 📋 Available Operations

### ✅ **Product Operations**

#### Queries
- `products(merchantId: String)` - Get all products for a merchant
- `product(id: ID!, merchantId: String)` - Get single product

#### Mutations
- `createProduct(createProductInput: CreateProductDto!)` - Create new product
- `updateProduct(id: ID!, updateProductInput: UpdateProductDto!)` - Update product
- `removeProduct(id: ID!)` - Delete product

### ✅ **Category Operations**

#### Queries
- `categories(merchantId: String)` - Get all categories for a merchant
- `category(id: ID!, merchantId: String)` - Get single category

#### Mutations
- `createCategory(createCategoryInput: CreateCategoryDto!)` - Create new category
- `updateCategory(id: ID!, updateCategoryInput: UpdateCategoryDto!)` - Update category
- `removeCategory(id: ID!)` - Delete category

### ✅ **SubCategory Operations**

#### Queries
- `subCategories(categoryId: String!)` - Get all subcategories for a category
- `subCategory(id: ID!, categoryId: String!)` - Get single subcategory

#### Mutations
- `createSubCategory(createSubCategoryInput: CreateSubCategoryDto!)` - Create new subcategory
- `updateSubCategory(id: ID!, categoryId: String!, updateSubCategoryInput: UpdateSubCategoryDto!)` - Update subcategory
- `removeSubCategory(id: ID!, categoryId: String!)` - Delete subcategory

### ✅ **Variant Operations**

#### Queries
- `variants(productId: String!)` - Get all variants for a product
- `variant(id: ID!, productId: String!)` - Get single variant

#### Mutations
- `createVariant(createVariantInput: CreateVariantDto!)` - Create new variant
- `updateVariant(id: ID!, productId: String!, updateVariantInput: UpdateVariantDto!)` - Update variant
- `removeVariant(id: ID!, productId: String!)` - Delete variant

### ✅ **Merchant Operations**

#### Queries
- `merchants` - Get all merchants
- `merchant(id: ID!)` - Get single merchant

#### Mutations
- `createMerchant(createMerchantInput: CreateMerchantDto!)` - Create new merchant
- `updateMerchant(id: ID!, updateMerchantInput: UpdateMerchantDto!)` - Update merchant
- `removeMerchant(id: ID!)` - Delete merchant

### ✅ **Discount Operations**

#### Queries
- `discounts(merchantId: String)` - Get all discounts for a merchant
- `discount(id: ID!, merchantId: String)` - Get single discount

#### Mutations
- `createDiscount(createDiscountInput: CreateDiscountDto!)` - Create new discount
- `updateDiscount(id: ID!, updateDiscountInput: UpdateDiscountDto!)` - Update discount
- `removeDiscount(id: ID!)` - Delete discount

### ✅ **Subscription Plan Operations**

#### Queries
- `subscriptionPlans(productId: String!)` - Get all subscription plans for a product
- `subscriptionPlan(id: ID!, productId: String!)` - Get single subscription plan

#### Mutations
- `createSubscriptionPlan(createSubscriptionPlanInput: CreateSubscriptionPlanDto!)` - Create new subscription plan
- `updateSubscriptionPlan(id: ID!, productId: String!, updateSubscriptionPlanInput: UpdateSubscriptionPlanDto!)` - Update subscription plan
- `removeSubscriptionPlan(id: ID!, productId: String!)` - Delete subscription plan

### ✅ **Payment Plan Operations**

#### Queries
- `paymentPlans(productId: String!)` - Get all payment plans for a product
- `paymentPlan(id: ID!, productId: String!)` - Get single payment plan

#### Mutations
- `createPaymentPlan(createPaymentPlanInput: CreatePaymentPlanDto!)` - Create new payment plan
- `updatePaymentPlan(id: ID!, productId: String!, updatePaymentPlanInput: UpdatePaymentPlanDto!)` - Update payment plan
- `removePaymentPlan(id: ID!, productId: String!)` - Delete payment plan

### ✅ **Transaction Log Operations**

#### Queries
- `transactionLogs(entity: String, entityId: String, userId: String, action: String, limit: Int, offset: Int)` - Get all transaction logs with filters
- `transactionLog(id: ID!)` - Get single transaction log
- `transactionLogsByMerchant(merchantId: String, entity: String, entityId: String, action: String, limit: Int, offset: Int)` - Get transaction logs for a merchant

## 🎯 **Quick Examples**

### Create a Complete Product Workflow

1. **Create Merchant**
```graphql
mutation {
  createMerchant(createMerchantInput: {
    id: "merchant_test"
    name: "Test Store"
  }) {
    id
    name
  }
}
```

2. **Create Category**
```graphql
mutation {
  createCategory(createCategoryInput: {
    name: "Electronics"
    merchantId: "merchant_test"
  }) {
    id
    name
  }
}
```

3. **Create SubCategory**
```graphql
mutation {
  createSubCategory(createSubCategoryInput: {
    name: "Smartphones"
    categoryId: "CATEGORY_ID_FROM_STEP_2"
  }) {
    id
    name
  }
}
```

4. **Create Product**
```graphql
mutation {
  createProduct(createProductInput: {
    name: "iPhone 15"
    price: 99999
    sku: "IPHONE-15-128GB"
    categoryId: "CATEGORY_ID_FROM_STEP_2"
    subCategoryId: "SUBCATEGORY_ID_FROM_STEP_3"
    merchantId: "merchant_test"
    count: 50
    isInStore: true
    isOnline: true
    saleType: REGULAR
    productStatus: ACTIVE
  }) {
    id
    name
    price
  }
}
```

5. **Create Variant**
```graphql
mutation {
  createVariant(createVariantInput: {
    name: "iPhone 15 - 256GB"
    sku: "IPHONE-15-256GB"
    price: 109999
    productId: "PRODUCT_ID_FROM_STEP_4"
  }) {
    id
    name
    price
  }
}
```

## 🔗 **Useful Links**

- **GraphQL Playground**: http://ng-product-local.dev.dev1.ngnair.com:3070/graphql
- **REST API Documentation**: http://ng-product-local.dev.dev1.ngnair.com:3070/api
- **Health Check**: http://ng-product-local.dev.dev1.ngnair.com:3070/api/v1/health

---

*All GraphQL resolvers are now fully implemented and ready for use!*
