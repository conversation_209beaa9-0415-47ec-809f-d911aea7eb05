import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class CustomValue {
  @Field()
  key: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  value: string;

  @Field()
  type: string;

  @Field()
  category: string;

  @Field({ nullable: true })
  isSystem?: boolean;

  @Field()
  createdBy: string;

  @Field()
  lastUpdatedBy: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}
