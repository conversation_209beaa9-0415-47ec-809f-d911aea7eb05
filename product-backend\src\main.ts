import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  // Fix variable hoisting issue - define isDevOrTest first
  const isDevOrTest = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';

  // Create Fastify adapter for better performance
  const fastifyAdapter = new FastifyAdapter({
    logger: true,
    trustProxy: true, // Enable this to get real client IPs behind proxy
  });

  // Register cookie support
  await fastifyAdapter.getInstance().register(require('@fastify/cookie'), {
    secret: process.env.COOKIE_SECRET || 'default-secret-key-for-development',
    parseOptions: {
      decode: decodeURIComponent,
      maxAge: 86400000, // 24 hours
      httpOnly: false,
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax' // CSRF protection
    }
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
  );

  // Add global validation pipe
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  // Environment-based IP configuration - Docker containers need to bind to 0.0.0.0
  const isDocker = process.env.HOSTNAME?.length === 12 || process.env.DOCKER_ENV === 'true';
  const sourceIp = isDocker ? '0.0.0.0' : (isDevOrTest ? '127.0.0.1' : '0.0.0.0');
  const port = process.env.PORT || 3000;

  // IP Restriction Middleware - Only allow requests from specified SOURCE_IP
  app.use((req: any, res: any, next: any) => {
    const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || req.headers['x-real-ip'];
    const allowedIP = process.env.SOURCE_IP || '127.0.0.1';

    console.log(`🌐 Request from IP: ${clientIP}, Allowed IP: ${allowedIP}`);

    // In development, only allow requests from the specified SOURCE_IP (but allow localhost for Docker)
    if (isDevOrTest && !isDocker && clientIP !== allowedIP && clientIP !== '127.0.0.1' && clientIP !== '::1') {
      console.warn(`🚫 Blocked request from unauthorized IP: ${clientIP}`);
      return res.status(403).send({ error: 'Access denied from this IP address' });
    }

    next();
  });

  // Environment-based CORS configuration with proper port interpolation
  app.enableCors({
    origin: isDevOrTest ? `http://localhost:${port}` : 'https://your.production.frontend',
    credentials: true,
  });

  // Register multipart support for file uploads
  await app.register(require('@fastify/multipart'), {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB max per file
      files: 10, // max 10 files per request
    },
  });

  app.setGlobalPrefix('api/v1');

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Product API')
    .setDescription('API documentation for the Product backend. Authentication uses access_token cookies.')
    .setVersion('1.0')
    .addCookieAuth('access_token', {
      type: 'http',
      in: 'cookie',
      scheme: 'bearer'
    })
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const listen_port = process.env.LISTENING_PORT || process.env.PORT || 3070;

  console.log(`🚀 Server starting on ${sourceIp}:${listen_port}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔒 IP Restriction: Only ${process.env.SOURCE_IP || '127.0.0.1'} allowed`);
  console.log(`🌐 CORS enabled for: http://localhost:${port}`);
  console.log(`📚 Swagger docs available at: /api`);

  await app.listen(listen_port, sourceIp);
}
bootstrap();
