import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { ProductEntity } from '../../modules/product/entities/product.entity';

@ObjectType()
export class VariantEntity {
  @Field(() => ID)
  id: string;

  @Field()
  productId: string;

  @Field()
  name: string;

  @Field()
  sku: string;

  @Field(() => Int, { nullable: true })
  price?: number;

  @Field({ nullable: true })
  attributes?: string;

  @Field({ nullable: true })
  deletedAt?: Date;

  // Relations
  @Field(() => ProductEntity)
  product: ProductEntity;
}
