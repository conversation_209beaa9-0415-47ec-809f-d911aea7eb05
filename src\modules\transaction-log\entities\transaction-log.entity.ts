import { ObjectType, Field, ID } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType()
export class TransactionLogEntity {
  @Field(() => ID)
  id: string;

  @Field()
  action: string;

  @Field()
  entity: string;

  @Field()
  entityId: string;

  @Field({ nullable: true })
  userId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  oldValue?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  newValue?: any;

  @Field()
  createdAt: Date;
}
