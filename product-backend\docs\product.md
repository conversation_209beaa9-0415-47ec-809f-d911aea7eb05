# Prisma Schema Reference

This document describes the database schema for the Product Backend, as defined in `prisma/schema.prisma`.  
It provides an overview of all models, their fields, and relationships to help developers and collaborators understand the data structure.

---

## Models

### Merchant

| Field        | Type     | Attributes                | Description                  |
|--------------|----------|---------------------------|------------------------------|
| id           | String   | @id, @default(cuid())     | Primary key                  |
| name         | String   |                           | Merchant name                |
| products     | Product[]| Relation                  | Products owned by merchant   |
| categories   | Category[]| Relation                 | Categories owned by merchant |
| discounts    | Discount[]| Relation                 | Discounts by merchant        |

---

### Category

| Field          | Type         | Attributes                | Description                  |
|----------------|--------------|---------------------------|------------------------------|
| id             | String       | @id, @default(cuid())     | Primary key                  |
| name           | String       | @unique                   | Category name                |
| merchantId     | String       |                           | FK to Merchant               |
| subCategories  | SubCategory[]| Relation                  | Subcategories in category    |
| products       | Product[]    | Relation                  | Products in category         |
| merchant       | Merchant     | Relation                  | Linked merchant              |

---

### SubCategory

| Field        | Type         | Attributes                | Description                  |
|--------------|--------------|---------------------------|------------------------------|
| id           | String       | @id, @default(cuid())     | Primary key                  |
| name         | String       |                           | Subcategory name             |
| categoryId   | String       |                           | FK to Category               |
| category     | Category     | Relation                  | Linked category              |
| products     | Product[]    | Relation                  | Products in subcategory      |

---

### Product

| Field             | Type                | Attributes                | Description                  |
|-------------------|---------------------|---------------------------|------------------------------|
| id                | String              | @id, @default(cuid())     | Primary key                  |
| name              | String              |                           | Product name                 |
| price             | Int?                |                           | Product price                |
| count             | Int                 | @default(0)               | Inventory count              |
| sku               | String              |                           | Stock keeping unit           |
| categoryId        | String              |                           | FK to Category               |
| subCategoryId     | String?             |                           | FK to SubCategory            |
| brand             | String?             |                           | Product brand                |
| itemWeight        | String?             |                           | Weight                       |
| length            | String?             |                           | Length                       |
| width             | String?             |                           | Width                        |
| description       | String?             |                           | Description                  |
| isInStore         | Boolean             | @default(false)           | Available in store           |
| isOnline          | Boolean             | @default(false)           | Available online             |
| productImages     | Json?               |                           | Images (array/object)        |
| createdAt         | DateTime            | @default(now())           | Creation timestamp           |
| deletedAt         | DateTime?           |                           | Deletion timestamp           |
| merchantId        | String              |                           | FK to Merchant               |
| productStatus     | ProductStatus?      | @default(INACTIVE)        | Product status (enum)        |
| saleType          | SaleType            | @default(CASH)            | Sale type: CASH, SUBSCRIPTION, or INSTALLMENT |
| subscriptionPlan  | SubscriptionPlan?   | Relation                  | Linked subscription plan     |
| paymentPlan       | PaymentPlan?        | Relation                  | Linked payment plan          |
| variants          | Variant[]           | Relation                  | Product variants             |
| category          | Category            | Relation                  | Linked category              |
| subCategory       | SubCategory?        | Relation                  | Linked subcategory           |
| merchant          | Merchant            | Relation                  | Linked merchant              |
| discounts         | Discount[]          | Relation                  | Discounts applied to product |

**Unique Constraint:**  
- `@@unique([sku, merchantId])` — SKU must be unique per merchant.

---

### Variant

| Field        | Type     | Attributes                | Description                  |
|--------------|----------|---------------------------|------------------------------|
| id           | String   | @id, @default(cuid())     | Primary key                  |
| productId    | String   |                           | FK to Product                |
| name         | String   |                           | Variant name                 |
| sku          | String   |                           | Variant SKU                  |
| price        | Int?     |                           | Variant price                |
| attributes   | Json?    |                           | Additional attributes        |
| deletedAt    | DateTime?|                           | Deletion timestamp           |
| product      | Product  | Relation                  | Linked product               |

**Unique Constraint:**  
- `@@unique([productId, sku])` — SKU must be unique per product.

---

### SubscriptionPlan

| Field                  | Type     | Attributes                | Description                  |
|------------------------|----------|---------------------------|------------------------------|
| id                     | String   | @id, @default(cuid())     | Primary key                  |
| productId              | String   | @unique                   | FK to Product                |
| recurringMode          | String   |                           | e.g., monthly, yearly        |
| recurringInterval      | Int      |                           | Interval (e.g., every 1)     |
| recurringFrequency     | Int      |                           | Frequency (e.g., 12 times)   |
| recurringTotalCycles   | Int      |                           | Total cycles                 |
| recurringTrialDays     | Int?     |                           | Trial days                   |
| recurringSetupFee      | Int?     |                           | Setup fee                    |
| recurringRefundable    | Boolean  | @default(false)           | Is refundable                |
| product                | Product  | Relation                  | Linked product               |

---

### PaymentPlan

| Field               | Type     | Attributes                | Description                  |
|---------------------|----------|---------------------------|------------------------------|
| id                  | String   | @id, @default(cuid())     | Primary key                  |
| productId           | String   | @unique                   | FK to Product                |
| totalAmount         | Int      |                           | Total payment amount         |
| installmentAmount   | Int      |                           | Amount per installment       |
| frequency           | String   |                           | Payment frequency            |
| totalInstallments   | Int      |                           | Number of installments       |
| gracePeriodDays     | Int?     |                           | Grace period in days         |
| lateFee             | Int?     |                           | Late fee                     |
| refundable          | Boolean  | @default(false)           | Is refundable                |
| product             | Product  | Relation                  | Linked product               |
| name                | String   |                           | Plan name                    |
| description         | String?  |                           | Plan description             |
| status              | PaymentPlanStatus | @default(ACTIVE) | Plan status (enum)           |
| balance             | Int      | @default(0)               | Remaining balance            |
| schedule            | Json?    |                           | Installment schedule         |
| createdAt           | DateTime | @default(now())           | Creation timestamp           |
| updatedAt           | DateTime | @updatedAt                | Update timestamp             |

---

### Discount

| Field         | Type           | Attributes                | Description                                      |
|---------------|----------------|---------------------------|--------------------------------------------------|
| id            | String         | @id, @default(cuid())     | Primary key                                      |
| name          | String         |                           | Discount name                                    |
| description   | String?        |                           | Discount description                             |
| type          | DiscountType   |                           | Discount type (enum: FLAT, PERCENTAGE)           |
| discount      | Int?           |                           | Discount value                                   |
| maxDiscount   | Int?           |                           | Maximum discount                                 |
| validFrom     | DateTime?      |                           | Start date                                       |
| validTo       | DateTime?      |                           | End date                                         |
| maxClaims     | Int?           |                           | Max number of claims                             |
| claims        | Int?           |                           | Number of claims used                            |
| status        | DiscountStatus |                           | Discount status (enum: ACTIVE, INACTIVE, EXPIRED, SCHEDULED) |
| scope         | String         |                           | product or purchase                              |
| createdAt     | DateTime       | @default(now())           | Creation timestamp                               |
| deletedAt     | DateTime?      |                           | Deletion timestamp                               |
| merchantId    | String         |                           | FK to Merchant                                   |
| merchant      | Merchant       | Relation                  | Linked merchant                                  |
| products      | Product[]      | Relation                  | Products this discount applies to                |

---


## Relationships Overview

- **Merchant** has many **Products**, **Categories**, and **Discounts**.
- **Category** has many **SubCategories** and **Products**.
- **Product** can have many **SubscriptionPlans**, many **PaymentPlans**, and many **Variants**.
- **Variant** belongs to a **Product**.
- **Discount** belongs to a **Merchant** and can be applied to many Products (many-to-many relation).

---

## Enums

### SubscriptionPlanStatus
| Value    | Description         |
|----------|---------------------|
| ACTIVE   | Plan is active      |
| INACTIVE | Plan is inactive    |

### PaymentPlanStatus
| Value    | Description         |
|----------|---------------------|
| ACTIVE   | Plan is active      |
| INACTIVE | Plan is inactive    |

### PaymentPlanFrequency
| Value    | Description         |
|----------|---------------------|
| WEEKLY   | Weekly payments     |
| MONTHLY  | Monthly payments    |
| YEARLY   | Yearly payments     |

---

## Authentication

- All protected endpoints require a JWT token in the `Authorization` header:
  ```
  Authorization: Bearer <your-jwt-token>
  ```

---

## Notes

- The `Discount` model now supports advanced, rule-based discounts and campaigns, and can be applied to multiple products via a many-to-many relation.
- Product status and discount type/status are now enums for better data integrity.
- All models use `cuid()` for unique IDs.
- All timestamps are in UTC.

## Discount-Product Association Rules

- When creating or updating a discount, you can associate it with products by providing a list of product IDs.
- The backend enforces that all products associated with a discount must belong to the same merchant as the discount.
- If you attempt to associate a discount with products from another merchant, the request will be rejected with a clear error message.
- Similarly, when creating or updating a product, all discount IDs provided must belong to the same merchant as the product.

## Error Handling for Cross-Merchant Relations

- If you try to associate a discount with a product from another merchant, or vice versa, you will receive a 400 Bad Request error with a message such as:
  - "One or more products are invalid or do not belong to this merchant."
  - "One or more discounts are invalid or do not belong to this merchant."

This ensures data integrity and prevents cross-merchant associations.

---

**For more details, see `prisma/schema.prisma` in the repository.**

# Product Backend API Guide for Frontend Collaborators

This document explains how the frontend should interact with the Product Backend, what endpoints are available, and what is expected for requests and responses.

---

## Base URL
http://localhost:3070/api/v1

## How to Use the Product Backend from the Frontend

---

### Authentication

- All requests to protected endpoints must include a valid JWT token in the `Authorization` header:
  ```
  Authorization: Bearer <your-jwt-token>
  ```
- The JWT must contain the `merchantId` of the authenticated user.

---

# Product Feature
## Endpoints
| Action                | Method | Endpoint                                               | Params/Body                                      | Description                                 |
|-----------------------|--------|--------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all products      | GET    | `/products?merchantId=MERCHANT_ID`                     | Query: `merchantId`                              | List all products for a merchant            |
| Get single product    | GET    | `/products/:id?merchantId=MERCHANT_ID`                 | Path: `id`, Query: `merchantId`                  | Get one product by ID for a merchant        |
| Create product        | POST   | `/products`                                            | Body: see below                                  | Create a new product                        |
| Update product        | PATCH  | `/products/:id?merchantId=MERCHANT_ID`                 | Path: `id`, Query: `merchantId`, Body: fields    | Update a product (must match merchant)      |
| Delete product        | DELETE | `/products/:id?merchantId=MERCHANT_ID`                 | Path: `id`, Query: `merchantId`                  | Delete a product (must match merchant)      |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new product                        |   ✅   |
| findAll     | Get all products for a merchant             |   ✅   |
| findOne     | Get a single product by id and merchant     |   ✅   |
| update      | Update a product by id and merchant         |   ✅   |
| remove      | Delete a product by id and merchant         |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples

### Create Product (`POST /products`)
```json
{
  "name": "Wireless Mouse",
  "price": 1999,
  "count": 100,
  "sku": "WM-001-BLK",
  "categoryId": "CATEGORY_ID",
  "subCategoryId": "SUBCATEGORY_ID",
  "brand": "Logitech",
  "itemWeight": "120g",
  "length": "12cm",
  "width": "6cm",
  "description": "A high-precision wireless mouse",
  "isInStore": true,
  "isOnline": true,
  "productImages": [
    "https://example.com/images/wm-001-front.jpg",
    "https://example.com/images/wm-001-side.jpg"
  ],
  "merchantId": "MERCHANT_ID",
  "productStatus": "ACTIVE",
  "saleType": "CASH",
  "attributes": {
    "color": "Black",
    "connectivity": "Bluetooth"
  },
  "discounts": ["DISCOUNT_ID_1", "DISCOUNT_ID_2"]
}
```

### Update Product (`PATCH /products/:id?merchantId=MERCHANT_ID`)
```json
{
  "name": "Updated Product Name",
  "price": 1899,
  "count": 80,
  "sku": "WM-001-BLK-NEW",
  "brand": "Logitech",
  "itemWeight": "115g",
  "length": "12.5cm",
  "width": "6.2cm",
  "description": "Updated description",
  "isInStore": false,
  "isOnline": true,
  "productImages": [
    "https://example.com/images/wm-001-front.jpg"
  ],
  "productStatus": "INACTIVE",
  "saleType": "SUBSCRIPTION",
  "attributes": {
    "color": "White"
  },
  "discounts": ["DISCOUNT_ID_1"]
}
```
# Product Feature
## SaleType Enum

| Value         | Description                                 |
|-------------- |---------------------------------------------|
| CASH          | One-time/cash/direct sale (no plan)          |
| SUBSCRIPTION  | Recurring subscription plan only             |
| INSTALLMENT   | Installment/payment plan only                |

Set `saleType` to control which plan (if any) is allowed for the product. The backend enforces that only the correct plan is set for each type.

# Category Feature
## Endpoints
| Action                | Method | Endpoint                                               | Params/Body                                      | Description                                 |
|-----------------------|--------|--------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all categories    | GET    | `/categories?merchantId=MERCHANT_ID`                   | Query: `merchantId`                              | List all categories for a merchant          |
| Get single category   | GET    | `/categories/:id?merchantId=MERCHANT_ID`               | Path: `id`, Query: `merchantId`                  | Get one category by ID for a merchant       |
| Create category       | POST   | `/categories`                                          | Body: see below                                 | Create a new category                       |
| Update category       | PATCH  | `/categories/:id?merchantId=MERCHANT_ID`               | Path: `id`, Query: `merchantId`, Body: fields    | Update a category (must match merchant)     |
| Delete category       | DELETE | `/categories/:id?merchantId=MERCHANT_ID`               | Path: `id`, Query: `merchantId`                  | Delete a category (must match merchant)     |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new category                       |   ✅   |
| findAll     | Get all categories for a merchant           |   ✅   |
| findOne     | Get a single category by id and merchant    |   ✅   |
| update      | Update a category by id and merchant        |   ✅   |
| remove      | Delete a category by id and merchant        |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples
### Create Category (`POST /categories`)
```json
{
  "name": "Laptops",
  "merchantId": "MERCHANT_ID"
}
```

### Update Category (`PATCH /categories/:id?merchantId=MERCHANT_ID`)
```json
{
  "name": "Updated Category Name"
}
```

# Subcategory Feature
## Endpoints
| Action                | Method | Endpoint                                                                 | Params/Body                                      | Description                                 |
|-----------------------|--------|--------------------------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all subcategories | GET    | `/categories/:categoryId/subcategories`                                  | Path: `categoryId`                              | List all subcategories for a category       |
| Get single subcategory| GET    | `/categories/:categoryId/subcategories/:id`                              | Path: `categoryId`, `id`                        | Get one subcategory by ID for a category    |
| Create subcategory    | POST   | `/categories/:categoryId/subcategories`                                  | Path: `categoryId`, Body: see below             | Create a new subcategory                    |
| Update subcategory    | PATCH  | `/categories/:categoryId/subcategories/:id`                              | Path: `categoryId`, `id`, Body: fields          | Update a subcategory (must match category)  |
| Delete subcategory    | DELETE | `/categories/:categoryId/subcategories/:id`                              | Path: `categoryId`, `id`                        | Delete a subcategory (must match category)  |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new subcategory                    |   ✅   |
| findAll     | Get all subcategories for a category        |   ✅   |
| findOne     | Get a single subcategory by id and category |   ✅   |
| update      | Update a subcategory by id and category     |   ✅   |
| remove      | Delete a subcategory by id and category     |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples
### Create Subcategory (`POST /categories/:categoryId/subcategories`)
```json
{
  "name": "Gaming Laptops"
}
```

### Update Subcategory (`PATCH /categories/:categoryId/subcategories/:id`)
```json
{
  "name": "Updated Subcategory Name"
}
```

# Variant Feature
## Endpoints
| Action                | Method | Endpoint                                               | Params/Body                                      | Description                                 |
|-----------------------|--------|--------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all variants      | GET    | `/products/:productId/variants`                        | Path: `productId`                                | List all variants for a product             |
| Get single variant    | GET    | `/products/:productId/variants/:id`                    | Path: `productId`, `id`                          | Get one variant by ID for a product         |
| Create variant        | POST   | `/products/:productId/variants`                        | Path: `productId`, Body: see below               | Create a new variant                        |
| Update variant        | PATCH  | `/products/:productId/variants/:id`                    | Path: `productId`, `id`, Body: fields            | Update a variant (must match product)       |
| Delete variant        | DELETE | `/products/:productId/variants/:id`                    | Path: `productId`, `id`                          | Delete a variant (must match product)       |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new variant                        |   ✅   |
| findAll     | Get all variants for a product              |   ✅   |
| findOne     | Get a single variant by id and product      |   ✅   |
| update      | Update a variant by id and product          |   ✅   |
| remove      | Delete a variant by id and product          |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples
### Create Variant (`POST /products/:productId/variants`)
```json
{
  "name": "Large Size",
  "sku": "SKU-12345-L",
  "price": 150,
  "attributes": {
    "color": "Red",
    "size": "Large"
  }
}
```

### Update Variant (`PATCH /products/:productId/variants/:id`)
```json
{
  "name": "Updated Variant Name",
  "sku": "SKU-12345-XL",
  "price": 200,
  "attributes": {
    "color": "Blue",
    "size": "XLarge"
  }
}
```
# Discount Feature

## Endpoints
| Action                | Method | Endpoint                                               | Params/Body                                      | Description                                 |
|-----------------------|--------|--------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all discounts     | GET    | `/discounts?merchantId=MERCHANT_ID`                    | Query: `merchantId`                              | List all discounts for a merchant           |
| Get single discount   | GET    | `/discounts/:id?merchantId=MERCHANT_ID`                | Path: `id`, Query: `merchantId`                  | Get one discount by ID for a merchant       |
| Create discount       | POST   | `/discounts`                                           | Body: see below                                  | Create a new discount                       |
| Update discount       | PATCH  | `/discounts/:id?merchantId=MERCHANT_ID`                | Path: `id`, Query: `merchantId`, Body: fields    | Update a discount (must match merchant)     |
| Delete discount       | DELETE | `/discounts/:id?merchantId=MERCHANT_ID`                | Path: `id`, Query: `merchantId`                  | Delete a discount (must match merchant)     |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new discount                       |   ✅   |
| findAll     | Get all discounts for a merchant            |   ✅   |
| findOne     | Get a single discount by id and merchant    |   ✅   |
| update      | Update a discount by id and merchant        |   ✅   |
| remove      | Delete a discount by id and merchant        |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples
### Create Discount (`POST /discounts`)
```json
{
  "name": "Keyboard Launch Discount",
  "description": "10% off on all keyboards",
  "type": "PERCENTAGE",
  "discount": 10,
  "maxDiscount": 30,
  "validFrom": "2025-09-01T00:00:00.000Z",
  "validTo": "2025-09-30T23:59:59.000Z",
  "maxClaims": 100,
  "claims": 0,
  "status": "ACTIVE",
  "scope": "product",
  "merchantId": "MERCHANT_ID",
  "products": ["PRODUCT_ID_1", "PRODUCT_ID_2"]
}
```

### Update Discount (`PATCH /discounts/:id?merchantId=MERCHANT_ID`)
```json
{
  "description": "Flat $5 off on all keyboards",
  "type": "FLAT",
  "discount": 5,
  "maxDiscount": 5,
  "validFrom": "2025-09-01T00:00:00.000Z",
  "validTo": "2025-09-30T23:59:59.000Z",
  "maxClaims": 50,
  "claims": 0,
  "status": "ACTIVE",
  "scope": "product",
  "products": ["PRODUCT_ID_1", "PRODUCT_ID_2"]
}
```
# Subscription Plan Feature

## Endpoints
| Action                      | Method | Endpoint                                                      | Params/Body                                      | Description                                 |
|-----------------------------|--------|---------------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all subscription plans  | GET    | `/products/:productId/subscription-plans`                     | Path: `productId`                                | List all subscription plans for a product   |
| Get single subscription plan| GET    | `/subscription-plans/:id`                                     | Path: `id`                                       | Get one subscription plan by ID             |
| Create subscription plan    | POST   | `/subscription-plans`                                         | Body: see below                                  | Create a new subscription plan              |
| Update subscription plan    | PATCH  | `/subscription-plans/:id`                                     | Path: `id`, Body: fields                         | Update a subscription plan                  |
| Delete subscription plan    | DELETE | `/subscription-plans/:id`                                     | Path: `id`                                       | Delete a subscription plan                  |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new subscription plan              |   ✅   |
| findAll     | Get all subscription plans for a product    |   ✅   |
| findOne     | Get a single subscription plan by id        |   ✅   |
| update      | Update a subscription plan by id            |   ✅   |
| remove      | Delete a subscription plan by id            |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples

### Create Subscription Plan (`POST /subscription-plans`)
```json
{
  "productId": "PRODUCT_ID",
  "name": "Monthly Gold Plan",
  "description": "Access to premium features, billed monthly.",
  "status": "ACTIVE",
  "recurringMode": "MONTH",
  "recurringInterval": 1,
  "recurringFrequency": 12,
  "recurringTotalCycles": 12,
  "recurringTrialDays": 14,
  "recurringSetupFee": 500,
  "recurringRefundable": true
}
```

### Update Subscription Plan (`PATCH /subscription-plans/:id`)
```json
{
  "name": "Yearly Gold Plan",
  "recurringMode": "YEAR",
  "recurringInterval": 1,
  "recurringFrequency": 1,
  "recurringTotalCycles": 1,
  "recurringTrialDays": 30,
  "recurringSetupFee": 1000,
  "recurringRefundable": false
}
```

---

# Payment Plan Feature

## Endpoints
| Action                  | Method | Endpoint                                                      | Params/Body                                      | Description                                 |
|-------------------------|--------|---------------------------------------------------------------|--------------------------------------------------|---------------------------------------------|
| Get all payment plans   | GET    | `/products/:productId/payment-plans`                          | Path: `productId`                                | List all payment plans for a product        |
| Get single payment plan | GET    | `/payment-plans/:id`                                          | Path: `id`                                       | Get one payment plan by ID                  |
| Create payment plan     | POST   | `/payment-plans`                                              | Body: see below                                  | Create a new payment plan                   |
| Update payment plan     | PATCH  | `/payment-plans/:id`                                          | Path: `id`, Body: fields                         | Update a payment plan                       |
| Delete payment plan     | DELETE | `/payment-plans/:id`                                          | Path: `id`                                       | Delete a payment plan                       |

## Methods Exist and Status
| Method      | Description                                 | Status |
|-------------|---------------------------------------------|--------|
| create      | Create a new payment plan                   |   ✅   |
| findAll     | Get all payment plans for a product         |   ✅   |
| findOne     | Get a single payment plan by id             |   ✅   |
| update      | Update a payment plan by id                 |   ✅   |
| remove      | Delete a payment plan by id                 |   ✅   |
All methods are currently working as expected.

## Method JSON Format Examples

### Create Payment Plan (`POST /payment-plans`)
```json
{
  "productId": "PRODUCT_ID",
  "totalAmount": 12000,
  "installmentAmount": 1000,
  "frequency": "MONTH",
  "totalInstallments": 12,
  "gracePeriodDays": 5,
  "lateFee": 100,
  "refundable": true,
  "balance": 12000
}
```

### Update Payment Plan (`PATCH /payment-plans/:id`)
```json
{
  "installmentAmount": 1500,
  "totalInstallments": 8,
  "gracePeriodDays": 7,
  "lateFee": 200,
  "refundable": false,
  "balance": 6000
}
  
### General Usage Notes

- **Always include the JWT token** in the `Authorization` header for protected endpoints.
- **Always provide the correct `merchantId`** as a query parameter or in the body where required.
- The backend will reject requests if the `merchantId` does not match the authenticated user.
- All create/update/delete actions are validated for merchant ownership.

---

### Error Handling

- `400 Bad Request`: Missing or invalid parameters, or merchant mismatch.
- `401 Unauthorized`: Missing or invalid JWT token.
- `403 Forbidden`: Attempt to access resources not owned by the merchant.
- `404 Not Found`: Resource does not exist.

---

### API Documentation

- Visit [http://localhost:3070/api/docs](http://localhost:3070/api/docs) for interactive Swagger UI documentation and testing.

---

### (For GET and DELETE endpoints)

- **GET** and **DELETE** endpoints do **not** require a request body.  
  - For GET, use query/path parameters as described above.
  - For DELETE, use the path and query parameters.

**Replace all placeholder values (like `YOUR_CATEGORY_ID`, `YOUR_MERCHANT_ID`, etc.) with actual IDs from your

