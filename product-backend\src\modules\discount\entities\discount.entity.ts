import { ObjectType, Field, ID, Int, registerEnumType } from '@nestjs/graphql';
import { DiscountStatus, DiscountType } from '@prisma/client';
import { MerchantEntity } from '../../merchant/entities/merchant.entity';
import { ProductEntity } from '../../product/entities/product.entity';

// Register enums for GraphQL
registerEnumType(DiscountStatus, {
  name: 'DiscountStatus',
});

registerEnumType(DiscountType, {
  name: 'DiscountType',
});

@ObjectType()
export class DiscountEntity {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => DiscountType)
  type: DiscountType;

  @Field(() => Int, { nullable: true })
  discount?: number;

  @Field(() => Int, { nullable: true })
  maxDiscount?: number;

  @Field({ nullable: true })
  validFrom?: Date;

  @Field({ nullable: true })
  validTo?: Date;

  @Field(() => Int, { nullable: true })
  maxClaims?: number;

  @Field(() => Int, { nullable: true })
  claims?: number;

  @Field(() => DiscountStatus)
  status: DiscountStatus;

  @Field()
  scope: string;

  @Field()
  createdAt: Date;

  @Field({ nullable: true })
  deletedAt?: Date;

  @Field()
  merchantId: string;

  // Relations
  @Field(() => MerchantEntity)
  merchant: MerchantEntity;

  @Field(() => [ProductEntity])
  products: ProductEntity[];
}
