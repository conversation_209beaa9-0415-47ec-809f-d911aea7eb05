import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class SupportFile {
  @Field()
  id: string;

  @Field()
  filename: string;

  @Field(() => Int)
  fileSize: number;

  @Field()
  fileUrl: string;

  @Field()
  mimeType: string;

  @Field()
  uploadedBy: string;

  @Field({ nullable: true })
  ticketId?: string;

  @Field({ nullable: true })
  commentId?: string;

  @Field()
  uploadedAt: Date;
}
