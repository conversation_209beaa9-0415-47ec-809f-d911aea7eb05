import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { Prisma } from '@prisma/client';
import { TransactionLogService } from '../transaction-log/transaction-log.service';

@Injectable()
export class DiscountService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly transactionLogService: TransactionLogService,
  ) {}

  async create(dto: CreateDiscountDto) {
    // Prevent duplicate discount name for the same merchant
    const existing = await this.prisma.discount.findFirst({
      where: {
        name: dto.name,
        merchantId: dto.merchantId,
      },
    });
    if (existing) {
      throw new BadRequestException('A discount with this name already exists for this merchant.');
    }
    try {
      const created = await this.prisma.discount.create({ data: dto });
      // Audit log
      await this.transactionLogService.createLog({
        action: 'CREATE',
        entity: 'Discount',
        entityId: created.id,
        userId: dto.merchantId,
        newValue: created,
      });
      return created;
    } catch (error: any) {
      throw new BadRequestException(error?.message || 'Failed to create discount');
    }
  }

  async findAll(merchantId: string) {
    return this.prisma.discount.findMany({ where: { merchantId } });
  }

  async findOne(id: string, merchantId: string) {
    const discount = await this.prisma.discount.findUnique({ where: { id } });
    if (!discount) throw new NotFoundException('Discount not found');
    if (discount.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');
    return discount;
  }

  async update(id: string, merchantId: string, dto: UpdateDiscountDto) {
    const before = await this.prisma.discount.findUnique({ where: { id } });
    if (!before) throw new NotFoundException('Discount not found');
    if (before.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

    // Validate that all products belong to the same merchant as the discount
    const { products, ...rest } = dto as any;
    if (products && products.length > 0) {
      const foundProducts = await this.prisma.product.findMany({
        where: { id: { in: products } },
        select: { id: true, merchantId: true },
      });
      const invalid = foundProducts.some((p) => p.merchantId !== merchantId);
      if (invalid || foundProducts.length !== products.length) {
        throw new BadRequestException('One or more products are invalid or do not belong to this merchant.');
      }
    }
    const data: Prisma.DiscountUpdateInput = {
      ...rest,
      products: products ? { set: products.map((id: string) => ({ id })) } : undefined,
    };

    try {
      const updated = await this.prisma.discount.update({ where: { id }, data });
      // Audit log
      await this.transactionLogService.createLog({
        action: 'UPDATE',
        entity: 'Discount',
        entityId: updated.id,
        userId: merchantId,
        oldValue: before,
        newValue: updated,
      });
      return updated;
    } catch (error: any) {
      throw new BadRequestException(error?.message || 'Failed to update discount');
    }
  }

  async remove(id: string, merchantId: string) {
    const before = await this.prisma.discount.findUnique({ where: { id } });
    if (!before) throw new NotFoundException('Discount not found');
    if (before.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');
    const deleted = await this.prisma.discount.delete({ where: { id } });
    // Audit log
    await this.transactionLogService.createLog({
      action: 'DELETE',
      entity: 'Discount',
      entityId: deleted.id,
      userId: merchantId,
      oldValue: before,
    });
    return deleted;
  }
}
