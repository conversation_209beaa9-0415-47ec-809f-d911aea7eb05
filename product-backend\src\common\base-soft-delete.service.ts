import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

/**
 * Base service class providing soft delete functionality
 * This class provides common methods for soft delete operations
 */
@Injectable()
export abstract class BaseSoftDeleteService {
  constructor(protected readonly prisma: PrismaService) {}

  /**
   * Get the model name for Prisma operations
   * Must be implemented by child classes
   */
  protected abstract getModelName(): string;

  /**
   * Get the Prisma model delegate
   * Must be implemented by child classes
   */
  protected abstract getModel(): any;

  /**
   * Soft delete a record by setting deletedAt timestamp
   */
  async softDelete(id: string, userId?: string): Promise<any> {
    try {
      const modelName = this.getModelName();
      const model = this.getModel();

      // Check if record exists and is not already soft deleted
      const existing = await model.findUnique({
        where: { id },
      });

      if (!existing) {
        throw new NotFoundException(`${modelName} not found`);
      }

      if (existing.deletedAt) {
        throw new BadRequestException(`${modelName} is already deleted`);
      }

      // Perform soft delete
      const deleted = await model.update({
        where: { id },
        data: { deletedAt: new Date() },
      });

      return deleted;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : `Failed to delete ${this.getModelName()}`
      );
    }
  }

  /**
   * Restore a soft deleted record by setting deletedAt to null
   */
  async restore(id: string): Promise<any> {
    try {
      const modelName = this.getModelName();
      const model = this.getModel();

      // Check if record exists and is soft deleted
      const existing = await model.findUnique({
        where: { id },
      });

      if (!existing) {
        throw new NotFoundException(`${modelName} not found`);
      }

      if (!existing.deletedAt) {
        throw new BadRequestException(`${modelName} is not deleted`);
      }

      // Restore the record
      const restored = await model.update({
        where: { id },
        data: { deletedAt: null },
      });

      return restored;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : `Failed to restore ${this.getModelName()}`
      );
    }
  }

  /**
   * Permanently delete a record (hard delete)
   * Only works on already soft deleted records
   */
  async permanentDelete(id: string): Promise<any> {
    try {
      const modelName = this.getModelName();
      const model = this.getModel();

      // Check if record exists and is soft deleted
      const existing = await model.findUnique({
        where: { id },
      });

      if (!existing) {
        throw new NotFoundException(`${modelName} not found`);
      }

      if (!existing.deletedAt) {
        throw new BadRequestException(`${modelName} must be soft deleted before permanent deletion`);
      }

      // Perform hard delete
      const deleted = await model.delete({
        where: { id },
      });

      return deleted;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : `Failed to permanently delete ${this.getModelName()}`
      );
    }
  }

  /**
   * Find all records excluding soft deleted ones
   */
  async findAllActive(where: any = {}, include?: any): Promise<any[]> {
    const model = this.getModel();
    return model.findMany({
      where: {
        ...where,
        deletedAt: null,
      },
      include,
    });
  }

  /**
   * Find all soft deleted records
   */
  async findAllDeleted(where: any = {}, include?: any): Promise<any[]> {
    const model = this.getModel();
    return model.findMany({
      where: {
        ...where,
        deletedAt: { not: null },
      },
      include,
    });
  }

  /**
   * Find a single active record by ID
   */
  async findActiveById(id: string, include?: any): Promise<any> {
    const model = this.getModel();
    const record = await model.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include,
    });

    if (!record) {
      throw new NotFoundException(`${this.getModelName()} not found`);
    }

    return record;
  }

  /**
   * Count active records
   */
  async countActive(where: any = {}): Promise<number> {
    const model = this.getModel();
    return model.count({
      where: {
        ...where,
        deletedAt: null,
      },
    });
  }

  /**
   * Count soft deleted records
   */
  async countDeleted(where: any = {}): Promise<number> {
    const model = this.getModel();
    return model.count({
      where: {
        ...where,
        deletedAt: { not: null },
      },
    });
  }
}
