# GraphQL Playground Documentation

## 🚀 Quick Start

Access the GraphQL Playground at: **http://localhost:3070/graphql**

## 🔐 Authentication

For mutations and protected queries, include the Authorization header:
```
{
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```

## 📋 Available Operations

### ✅ **Product Operations** (FULLY IMPLEMENTED)

#### 🔍 **Queries**

##### Get All Products
```graphql
query GetProducts($merchantId: String) {
  products(merchantId: $merchantId) {
    id
    name
    price
    sku
    count
    categoryId
    subCategoryId
    brand
    itemWeight
    length
    width
    description
    isInStore
    isOnline
    productImages
    createdAt
    deletedAt
    merchantId
    productStatus
    saleType
    category {
      id
      name
    }
    subCategory {
      id
      name
    }
    merchant {
      id
      name
    }
    variants {
      id
      name
      sku
      price
    }
    discounts {
      id
      name
      type
      discount
    }
    subscriptionPlans {
      id
      name
      recurringMode
    }
    paymentPlans {
      id
      name
      totalAmount
    }
  }
}
```

**Variables:**
```json
{
  "merchantId": "merchant_12345"
}
```

#### Get Single Product
```graphql
query GetProduct($id: ID!, $merchantId: String!) {
  product(id: $id, merchantId: $merchantId) {
    id
    name
    price
    sku
    count
    categoryId
    subCategoryId
    brand
    description
    isInStore
    isOnline
    productImages
    createdAt
    merchantId
    productStatus
    saleType
    category {
      id
      name
    }
    subCategory {
      id
      name
    }
    variants {
      id
      name
      sku
      price
      count
    }
  }
}
```

**Variables:**
```json
{
  "id": "prod_67890",
  "merchantId": "merchant_12345"
}
```

## Available Mutations

### 1. Product Mutations

#### Create Product
```graphql
mutation CreateProduct($createProductInput: CreateProductDto!) {
  createProduct(createProductInput: $createProductInput) {
    id
    name
    price
    sku
    count
    categoryId
    subCategoryId
    brand
    description
    isInStore
    isOnline
    productImages
    createdAt
    merchantId
    productStatus
    saleType
  }
}
```

**Variables:**
```json
{
  "createProductInput": {
    "name": "Premium Wireless Headphones",
    "price": 2999,
    "sku": "PWH-001-BLK",
    "categoryId": "cmfl41swn000brv01hlxhrwa8",
    "subCategoryId": "cmfl41swn000erv01h9963wi4",
    "brand": "TechSound Pro",
    "description": "High-quality wireless headphones with noise cancellation",
    "isInStore": true,
    "isOnline": true,
    "productImages": ["https://example.com/images/headphones-front.jpg"],
    "merchantId": "merchant_12345",
    "productStatus": "ACTIVE",
    "count": 150,
    "saleType": "REGULAR"
  }
}
```

#### Update Product
```graphql
mutation UpdateProduct($id: ID!, $updateProductInput: UpdateProductDto!) {
  updateProduct(id: $id, updateProductInput: $updateProductInput) {
    id
    name
    price
    sku
    count
    brand
    description
    isInStore
    isOnline
    productImages
    productStatus
    saleType
  }
}
```

**Variables:**
```json
{
  "id": "prod_67890",
  "updateProductInput": {
    "name": "Premium Wireless Headphones - Updated",
    "price": 3299,
    "description": "Updated description with new features"
  }
}
```

#### Delete Product
```graphql
mutation DeleteProduct($id: ID!) {
  removeProduct(id: $id)
}
```

**Variables:**
```json
{
  "id": "prod_67890"
}
```

## Data Types

### ProductStatus Enum
- `ACTIVE`
- `INACTIVE`
- `DRAFT`

### SaleType Enum
- `CASH`
- `REGULAR`
- `SUBSCRIPTION`
- `PAYMENT_PLAN`

## Error Handling

GraphQL errors are returned in the following format:
```json
{
  "errors": [
    {
      "message": "merchantId is required",
      "code": "BAD_REQUEST",
      "path": ["products"]
    }
  ]
}
```

## Common Error Messages
- `merchantId is required` - Missing merchant ID parameter
- `categoryId is required` - Missing category ID for product creation
- `Authentication required` - JWT token missing or invalid
- `Product not found` - Product with specified ID doesn't exist

## Testing Examples

### Example 1: Create and Query Product
1. First, create a product:
```graphql
  mutation {
    createProduct(createProductInput: {
      name: "Test Product"
      price: 1000
      sku: "TEST-001"
      categoryId: "cmfl41swn000brv01hlxhrwa8"
      merchantId: "merchant_test"
      isInStore: true
      isOnline: true
      count: 10
      saleType: REGULAR
      productStatus: ACTIVE
    }) {
      id
      name
      price
      sku
    }
  }
```

2. Then query the created product:
```graphql
query {
  product(id: "PRODUCT_ID_FROM_STEP_1", merchantId: "merchant_test") {
    id
    name
    price
    sku
    category {
      id
      name
    }
  }
}
```

## Notes for Frontend Developers

1. **Authentication**: All mutations require authentication. Queries for products are public but require merchantId.

2. **Merchant Scoping**: All data is scoped by merchantId. Always include the correct merchantId in queries.

3. **Relations**: Use field selection to include related data (category, subCategory, variants, etc.) in a single query.

4. **Error Handling**: Always check the `errors` array in the response for validation or business logic errors.

5. **Pagination**: Currently not implemented. All queries return full result sets.

## Missing GraphQL Implementations

The following REST endpoints do not have GraphQL equivalents yet and should be implemented:

### Categories (Missing Resolver)
**REST Endpoints:**
- `GET /categories?merchantId=xxx` → Should be `query { categories(merchantId: String!) }`
- `POST /categories` → Should be `mutation { createCategory(input: CreateCategoryInput!) }`
- `GET /categories/:id` → Should be `query { category(id: ID!) }`
- `PATCH /categories/:id` → Should be `mutation { updateCategory(id: ID!, input: UpdateCategoryInput!) }`
- `DELETE /categories/:id` → Should be `mutation { deleteCategory(id: ID!) }`

**Example Implementation Needed:**
```graphql
# Queries
query GetCategories($merchantId: String!) {
  categories(merchantId: $merchantId) {
    id
    name
    merchantId
    subCategories {
      id
      name
    }
    products {
      id
      name
    }
  }
}

# Mutations
mutation CreateCategory($input: CreateCategoryInput!) {
  createCategory(input: $input) {
    id
    name
    merchantId
  }
}
```

### SubCategories (Missing Resolver)
**REST Endpoints:**
- `GET /categories/:categoryId/subcategories` → Should be `query { subCategories(categoryId: ID!) }`
- `POST /categories/:categoryId/subcategories` → Should be `mutation { createSubCategory(input: CreateSubCategoryInput!) }`

### Variants (Missing Resolver)
**REST Endpoints:**
- `GET /products/:productId/variants` → Should be `query { variants(productId: ID!) }`
- `POST /products/:productId/variants` → Should be `mutation { createVariant(input: CreateVariantInput!) }`

### Discounts (Missing Resolver)
**REST Endpoints:**
- `POST /discounts` → Should be `mutation { createDiscount(input: CreateDiscountInput!) }`
- `GET /discounts?merchantId=xxx` → Should be `query { discounts(merchantId: String!) }`

### Merchants (Missing Resolver)
**REST Endpoints:**
- `POST /merchants` → Should be `mutation { createMerchant(input: CreateMerchantInput!) }`
- `GET /merchants/all` → Should be `query { merchants }`

### Subscription Plans (Missing Resolver)
**REST Endpoints:**
- `POST /subscription-plans` → Should be `mutation { createSubscriptionPlan(input: CreateSubscriptionPlanInput!) }`
- `GET /subscription-plans?productId=xxx` → Should be `query { subscriptionPlans(productId: ID!) }`

### Payment Plans (Missing Resolver)
**REST Endpoints:**
- `POST /payment-plans` → Should be `mutation { createPaymentPlan(input: CreatePaymentPlanInput!) }`
- `GET /payment-plans?productId=xxx` → Should be `query { paymentPlans(productId: ID!) }`

### Transaction Logs (Missing Entity & Resolver)
**REST Endpoints:**
- `GET /transaction-logs?merchantId=xxx` → Should be `query { transactionLogs(merchantId: String!) }`

## Implementation Priority

**High Priority (Core Functionality):**
1. Categories - Essential for product organization
2. SubCategories - Essential for product organization
3. Variants - Essential for product variations
4. Merchants - Essential for multi-tenancy

**Medium Priority (Business Features):**
5. Discounts - Important for pricing
6. Subscription Plans - Important for recurring billing
7. Payment Plans - Important for installment billing

**Low Priority (Audit/Logging):**
8. Transaction Logs - Important for audit trails
