import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { User, JWTPayload, AuthConfig, DecryptedTokens } from './types/auth.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly config: AuthConfig;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    this.config = {
      authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      cookieNames: {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      },
    };
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   */
  private async decryptCookie(encryptedValue: string): Promise<string> {
    try {
      this.logger.log(`🔓 [AUTH SERVICE] Starting Rails-compatible decryption process...`);
      this.logger.log(`🔐 [AUTH SERVICE] Encrypted value length: ${encryptedValue.length}`);
      this.logger.log(`🔑 [AUTH SERVICE] Using encryption key: ${this.config.encryptionKey.substring(0, 16)}...`);
      this.logger.log(`🔐 [AUTH SERVICE] Raw encrypted value (first 100 chars): ${encryptedValue.substring(0, 100)}...`);

      // First, try to decode as URL-safe base64 (the token might be base64 encoded)
      let decodedValue: string;
      try {
        // Convert URL-safe base64 to standard base64
        const standardBase64 = encryptedValue.replace(/-/g, '+').replace(/_/g, '/');
        // Add padding if needed
        const paddedBase64 = standardBase64 + '='.repeat((4 - standardBase64.length % 4) % 4);
        decodedValue = Buffer.from(paddedBase64, 'base64').toString('utf8');
        this.logger.log(`✅ [AUTH SERVICE] Base64 decoded successfully, length: ${decodedValue.length}`);
        this.logger.log(`🔐 [AUTH SERVICE] Decoded value (first 100 chars): ${decodedValue.substring(0, 100)}...`);
      } catch (base64Error) {
        // If base64 decoding fails, try URL decoding
        this.logger.log('⚠️ [AUTH SERVICE] Base64 decoding failed, trying URL decoding...');
        this.logger.log(`🔐 [AUTH SERVICE] Base64 error: ${base64Error instanceof Error ? base64Error.message : String(base64Error)}`);
        decodedValue = decodeURIComponent(encryptedValue);
        this.logger.log(`🔐 [AUTH SERVICE] URL decoded length: ${decodedValue.length}`);
      }

      // Rails MessageEncryptor format: base64(encrypted_data)--base64(iv)--base64(auth_tag)
      const parts = decodedValue.split('--');
      this.logger.log(`🔐 [AUTH SERVICE] Split into ${parts.length} parts using Rails format (--)`);

      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        this.logger.error(`🔐 [AUTH SERVICE] First 100 chars of decoded value: ${decodedValue.substring(0, 100)}`);
        throw new Error('Invalid encrypted cookie format');
      }

      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      this.logger.log(`🔐 [AUTH SERVICE] Encrypted data length: ${encryptedData.length}, IV length: ${iv.length}, Auth tag length: ${authTag.length}`);

      // Convert hex key to buffer
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      this.logger.log(`🔑 [AUTH SERVICE] Key length: ${key.length} bytes`);

      // Create decipher for GCM mode
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.log(`✅ [AUTH SERVICE] Decryption successful, result length: ${decrypted.length}`);

      // Remove quotes if present (Rails adds quotes around JSON strings)
      const result = decrypted.replace(/^"(.*)"$/, '$1');
      this.logger.log(`✅ [AUTH SERVICE] Final result length after quote removal: ${result.length}`);

      return result;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Decryption failed: ${error instanceof Error ? error.message : String(error)}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
      throw new UnauthorizedException('Failed to decrypt authentication token');
    }
  }

  /**
   * Extract raw encrypted tokens from cookies (without decryption)
   */
  extractRawTokensFromCookies(cookies: Record<string, string>): { accessToken?: string; refreshToken?: string } {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    return {
      accessToken: encryptedAccessToken,
      refreshToken: encryptedRefreshToken,
    };
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  async extractTokensFromCookies(cookies: Record<string, string>): Promise<DecryptedTokens> {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    if (!encryptedRefreshToken) {
      this.logger.warn('Refresh token not found, proceeding with access token only');
    }

    try {
      const accessToken = await this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? await this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }

  /**
   * Verify JWT token using JWKS - temporarily simplified for testing
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      // Decode token header to get kid
      const decoded = jwt.decode(token, { complete: true });

      if (!decoded || !decoded.header) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Temporarily skip JWKS verification - just decode the payload
      this.logger.warn('JWKS verification temporarily disabled - only decoding JWT payload');
      const payload = decoded.payload as JWTPayload;

      // Basic validation
      if (!payload.sub || !payload.email) {
        throw new UnauthorizedException('Invalid JWT payload - missing required fields');
      }

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Extract user information from JWT payload
   * Maps the new JWT payload structure from the authentication service
   */
  getUserFromPayload(payload: JWTPayload): User {
    // Provide default role if not present in JWT
    const role = payload.role || 'merchant_user'; // Default to 'merchant_user' for product service

    this.logger.log(`📋 [AUTH SERVICE] User role from JWT: ${payload.role || 'undefined'}, using: ${role}`);

    // Extract merchant ID from ent_set if available
    let merchantId: string | undefined;
    if (payload.ent_set && typeof payload.ent_set === 'object') {
      // Try to extract merchant ID from entity set
      merchantId = payload.ent_set.merchantId || payload.ent_set.merchant_id;
    }
    // Fallback to direct merchantId field
    merchantId = merchantId || payload.merchantId;

    return {
      id: payload.sub,
      email: payload.email,
      username: payload.username || payload.email, // Use email as username if not provided
      firstName: payload.first_name || payload.firstName,
      lastName: payload.last_name || payload.lastName,
      role: role,
      permissions: payload.permissions || [],
      merchantId: merchantId,
      createdAt: new Date(payload.iat * 1000).toISOString(),
      updatedAt: new Date().toISOString(),

      // Additional fields from external auth service
      accounts: payload.accounts,
      partners: payload.partners,
      active_accounts: payload.active_accounts,
      active_partners: payload.active_partners,
      first_name: payload.first_name,
      last_name: payload.last_name,

      // Additional optional fields for User interface compliance
      phone: undefined, // Not provided in JWT
      country: undefined, // Not provided in JWT
      verifiedEmail: true, // Assume verified if JWT is valid
      verifiedPhone: false, // Default to false
      partnerId: undefined, // Could be extracted from partners array if needed
      mfaEnabled: payload.amr ? payload.amr.length > 1 : false, // Multiple auth methods = MFA
      active: true, // Assume active if JWT is valid
      accountId: undefined, // Could be extracted from accounts array if needed
      isAdmin: role === 'admin',
    };
  }

  /**
   * Ensure JWT payload has all required fields with proper defaults
   */
  private ensureCompletePayload(payload: JWTPayload): JWTPayload {
    const now = Math.floor(Date.now() / 1000);
    const sessionTtl = 24 * 60 * 60; // 24 hours in seconds

    // Generate secure random values for missing fields
    const generateSecureId = () => require('crypto').randomUUID();
    const generateSessionId = () => `sess_${require('crypto').randomBytes(8).toString('hex')}`;

    return {
      // Required standard JWT fields
      iss: payload.iss || process.env.JWT_ISSUER_URL || 'https://ngnair.com',
      sub: payload.sub, // This must exist from verification
      email: payload.email, // This must exist from verification
      aud: payload.aud || [process.env.JWT_AUDIENCE || 'partner'],
      exp: payload.exp || (now + sessionTtl),
      iat: payload.iat || now,
      jti: payload.jti || generateSecureId(),

      // Required custom fields
      sid: payload.sid || generateSessionId(),
      azp: payload.azp || 'webapp',
      ent_set: payload.ent_set || {},
      perm_v: payload.perm_v || 1,
      amr: payload.amr || ['pwd'],
      auth_time: payload.auth_time || now,

      // Optional fields (preserve if present)
      username: payload.username,
      firstName: payload.firstName,
      lastName: payload.lastName,
      role: payload.role,
      permissions: payload.permissions,
      merchantId: payload.merchantId,
      accounts: payload.accounts,
      partners: payload.partners,
      active_accounts: payload.active_accounts,
      active_partners: payload.active_partners,
      first_name: payload.first_name,
      last_name: payload.last_name,
    };
  }

  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract and decrypt tokens
      this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
      const { accessToken } = await this.extractTokensFromCookies(cookies);
      this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);

      // Verify access token
      this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
      const payload = await this.verifyToken(accessToken);
      this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);

      // Return user info
      this.logger.log('📋 [AUTH SERVICE] Getting user info from payload...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Authenticate user from cookies using proper external auth service integration
   * This is the main method for the /auth/me endpoint
   * Returns the complete JWT payload as required
   */
  async authenticateUserFromCookies(cookies: Record<string, string>): Promise<JWTPayload> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication for /auth/me endpoint');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract encrypted access token from cookies
      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log(`🔑 [AUTH SERVICE] Found encrypted access token: ${encryptedAccessToken.substring(0, 50)}...`);

      // Step 1: Decrypt the access token
      this.logger.log('🔓 [AUTH SERVICE] Step 1: Decrypting access token...');
      const decryptedToken = await this.decryptCookie(encryptedAccessToken);
      this.logger.log(`✅ [AUTH SERVICE] Token decrypted successfully, length: ${decryptedToken.length}`);

      // Step 2: Verify JWT using JWKS
      this.logger.log('🔍 [AUTH SERVICE] Step 2: Verifying JWT with JWKS...');
      const payload = await this.verifyToken(decryptedToken);
      this.logger.log(`✅ [AUTH SERVICE] JWT verified successfully for user: ${payload.sub}`);

      // Step 3: Ensure all required fields are present in the payload
      this.logger.log('📋 [AUTH SERVICE] Step 3: Validating JWT payload structure... [NEW METHOD]');
      const completePayload = this.ensureCompletePayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Complete JWT payload validated for user: ${completePayload.sub} [NEW METHOD]`);

      return completePayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Get user by ID from external auth service
   */
  async getUserById(userId: string, cookies: Record<string, string>): Promise<User> {
    this.logger.log(`🔍 [AUTH SERVICE] Getting user by ID: ${userId}`);
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users/${userId}`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Product-Service/1.0',
      };

      // Make request to external auth service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the user data from the response
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get user by ID from external service: ${error instanceof Error ? error.message : String(error)}`);
      if ((error as any).response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      if ((error as any).response?.status === 404) {
        throw new UnauthorizedException('User not found');
      }
      throw new UnauthorizedException('Failed to retrieve user information');
    }
  }

  /**
   * Get all users from external auth service
   */
  async getAllUsers(cookies: Record<string, string>): Promise<User[]> {
    this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Product-Service/1.0',
      };

      // Make request to external auth service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Users retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the users array from the response
      const usersData = Array.isArray(response.data) ? response.data : [response.data];
      return usersData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error instanceof Error ? error.message : String(error)}`);
      if ((error as any).response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Failed to retrieve users information');
    }
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }
}
