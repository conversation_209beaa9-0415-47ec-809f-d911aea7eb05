import { IsString, IsOptional, IsInt, IsEnum, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum DiscountType {
  FLAT = 'FLAT',
  PERCENTAGE = 'PERCENTAGE',
}

export enum DiscountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  EXPIRED = 'EXPIRED',
  SCHEDULED = 'SCHEDULED',
}

export class CreateDiscountDto {
  @ApiProperty({
    description: 'Discount name/title',
    example: 'Summer Sale 2024',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'Detailed description of the discount',
    example: 'Get 20% off on all electronics during summer season',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Type of discount calculation',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE
  })
  @IsEnum(DiscountType)
  type!: DiscountType;

  @ApiProperty({
    description: 'Discount amount (percentage for PERCENTAGE type, cents for FLAT type)',
    example: 20,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  discount?: number;

  @ApiProperty({
    description: 'Maximum discount amount in cents (for PERCENTAGE type)',
    example: 5000,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  maxDiscount?: number;

  @ApiProperty({
    description: 'Discount validity start date (ISO 8601 format)',
    example: '2024-06-01T00:00:00.000Z',
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsDateString()
  validFrom?: string;

  @ApiProperty({
    description: 'Discount validity end date (ISO 8601 format)',
    example: '2024-08-31T23:59:59.999Z',
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsDateString()
  validTo?: string;

  @ApiProperty({
    description: 'Maximum number of times this discount can be claimed',
    example: 1000,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  maxClaims?: number;

  @ApiProperty({
    description: 'Current number of claims (usually auto-calculated)',
    example: 0,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  claims?: number;

  @ApiProperty({
    description: 'Current status of the discount',
    enum: DiscountStatus,
    example: DiscountStatus.ACTIVE
  })
  @IsEnum(DiscountStatus)
  status!: DiscountStatus;

  @ApiProperty({
    description: 'Discount scope (e.g., "global", "category:electronics", "product:prod_123")',
    example: 'category:electronics',
    maxLength: 200
  })
  @IsString()
  scope!: string;

  @ApiProperty({
    description: 'Creation timestamp (ISO 8601 format) - usually auto-generated',
    example: '2024-01-15T10:30:00.000Z',
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsDateString()
  createdAt?: string;

  @ApiProperty({
    description: 'Deletion timestamp (ISO 8601 format) - for soft deletes',
    example: null,
    format: 'date-time',
    required: false
  })
  @IsOptional()
  @IsDateString()
  deletedAt?: string;

  @ApiProperty({
    description: 'Merchant ID that owns this discount',
    example: 'merchant_12345',
    format: 'uuid'
  })
  @IsString()
  merchantId!: string;
}
