import { Prisma } from "@prisma/client";

// Type-safe ticket types
export const ticketInclude = {
  category: true,
  comments: true,
  files: true
} as const;

export type TicketPayload = Prisma.TicketGetPayload<{
  include: typeof ticketInclude;
}>;

export type TicketCreateInput = Prisma.TicketCreateInput;
export type TicketUpdateInput = Prisma.TicketUpdateInput;
export type TicketWhereUniqueInput = Prisma.TicketWhereUniqueInput;
export type TicketWhereInput = Prisma.TicketWhereInput;
export type TicketOrderByWithRelationInput = Prisma.TicketOrderByWithRelationInput;

// Type-safe category types
export type CategoryPayload = Prisma.CategoryGetPayload<{
  select: Record<string, never>;
}>;
export type CategoryCreateInput = Prisma.CategoryCreateInput;
export type CategoryUpdateInput = Prisma.CategoryUpdateInput;
export type CategoryWhereUniqueInput = Prisma.CategoryWhereUniqueInput;
export type CategoryWhereInput = Prisma.CategoryWhereInput;
export type CategoryOrderByWithRelationInput = Prisma.CategoryOrderByWithRelationInput;
