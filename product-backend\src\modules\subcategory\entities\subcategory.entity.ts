import { ObjectType, Field, ID } from '@nestjs/graphql';
import { CategoryEntity } from '../../category/entities/category.entity';
import { ProductEntity } from '../../product/entities/product.entity';

@ObjectType()
export class SubCategoryEntity {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field()
  categoryId: string;

  // Relations
  @Field(() => CategoryEntity)
  category: CategoryEntity;

  @Field(() => [ProductEntity])
  products: ProductEntity[];
}
