import { Controller, Get, Post, Body, Param, Patch, Delete, Query, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { DiscountService } from './discount.service';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Discounts')
@Controller('discounts')
export class DiscountController {
  constructor(private readonly discountService: DiscountService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new discount' })
  @ApiBody({ type: CreateDiscountDto })
  @ApiResponse({ status: 201, description: 'Discount created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  create(@Body() dto: CreateDiscountDto) {
    if (!dto.merchantId) throw new BadRequestException('merchantId is required');
    return this.discountService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all discounts for a merchant' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Discounts retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  findAll(@Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.discountService.findAll(merchantId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a discount by ID' })
  @ApiParam({ name: 'id', description: 'Discount ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Discount retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Discount not found' })
  findOne(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.discountService.findOne(id, merchantId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a discount' })
  @ApiParam({ name: 'id', description: 'Discount ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiBody({ type: UpdateDiscountDto })
  @ApiResponse({ status: 200, description: 'Discount updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Discount not found' })
  update(@Param('id') id: string, @Query('merchantId') merchantId: string, @Body() dto: UpdateDiscountDto) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.discountService.update(id, merchantId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a discount' })
  @ApiParam({ name: 'id', description: 'Discount ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Discount deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Discount not found' })
  remove(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.discountService.remove(id, merchantId);
  }
}
