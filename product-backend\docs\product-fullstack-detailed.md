🧾 Product Microservice: Full Task List (Detailed) 

 

🏗️ PROJECT INITIALIZATION 

1. Repository & Environment Setup 

Initialize Git repository and set up monorepo (if applicable). 

Create NestJS project structure for Product Microservice. 

Add ESLint, Prettier, and <PERSON><PERSON> pre-commit hooks. 

Set up Docker environment with: 

PostgreSQL 

Prisma 

Adminer or pgAdmin (optional) 

Configure .env file for development, staging, and production environments. 

2. Prisma Setup 

Define schema in schema.prisma (already created). 

Run prisma generate and prisma migrate dev. 

Seed database with sample data for products, categories, subcategories, and discounts. 

 

🔧 CORE DOMAIN DEVELOPMENT 

3. Modules and Services 

Create these modules within NestJS: 

products 

categories 

subcategories 

variants 

discounts 

subscriptionPlans 

paymentPlans 

4. Entity & DTO Creation 

Create Prisma service and inject into relevant modules. 

Define DTOs for Create, Update, and Read operations. 

Apply class-validator decorators for validation rules. 

 

🔌 API LAYERS 

5. GraphQL Layer (Frontend & Admin) 

Implement GraphQL resolvers and schema definitions for all modules. 

Add filters, pagination, sorting, and nested resolvers. 

Secure queries/mutations with scoped guards using merchantID. 

6. REST API Layer (Microservice Communication) 

Define controllers and REST endpoints for: 

Product data access 

Variant lookup 

Discount retrieval 

Subscription/payment plan metadata fetch 

Use @Query, @Param, @Body, @Headers decorators properly. 

Add Swagger decorators for documentation. 

 

🔐 SECURITY & AUTHORIZATION 

7. Auth Integration 

Add middleware to extract and verify JWTs. 

Enforce merchant-level scoping across API endpoints. 

Use global guards or interceptors for token claims. 

8. Rate Limiting & API Gateway Compatibility 

Support throttling via NestJS throttler or API Gateway headers. 

 

🔍 SEARCH, PAGINATION, FILTERING 

9. Product and Discount Query Support 

Implement: 

Name/SKU full-text search 

Filtering by category, brand, price, availability 

Discount validity filtering (date, scope, type) 

Implement pagination with limit, offset, and total count. 

 

📈 ADMIN FEATURES 

10. Admin Config Interfaces 

Add GraphQL support for admin features: 

Create/update/delete any entity 

Bulk import (future scope) 

Audit log (future scope) 

 

🔄 INTERSERVICE COMMUNICATION 

11. Microservice REST APIs 

Expose endpoints for Finance service to: 

Retrieve payment plan data 

Get subscription terms 

Validate product price and discounts 

Secure via internal service tokens 

 

🧪 TESTING 

12. Unit and E2E Testing 

Add tests for each module with Jest: 

DTO validation 

Resolver/controller behavior 

Service logic coverage 

 

🛠️ DEVOPS & OBSERVABILITY 

13. Deployment & CI/CD 

Add Dockerfile and Docker Compose for microservice. 

Create GitHub Actions or GitLab pipeline: 

Lint 

Test 

Build & push image 

Setup Prometheus-compatible healthcheck endpoint 

 

📓 DOCUMENTATION 

14. Auto-Generate API Docs 

Use Swagger for REST documentation 

GraphQL SDL for schema generation 

README: setup, config, seed, migrations, build 

 

 

-------------------------------------------------------- 

 

Product Microservice – Deep Dive Architecture & Build Guide 

Comprehensive blueprint for design, implementation, and operations of the Product service powering a multi-tenant payments platform. Intended as a hand-off knowledge base for AI/engineering agents. 

 

1) Executive Summary 

The Product service is the merchant-scoped catalog authority for products, categories, subcategories, variants, discounts, subscriptions, and installment-based payment plans. It serves three major clients: 

Customer/Merchant Frontend UI via GraphQL for catalog browsing, purchasing flows, and merchant product management. 

Internal Admin Frontend UI (support and operations) via GraphQL (admin namespace) for high-level configuration, message/alert/template management, and enforcing merchant boundaries. 

Platform microservices (Finance, Accounts, etc.) via REST for stable, scoped, contract-first integrations. 

Key capabilities: 

Merchant-controlled catalog, categories, and variant modeling. 

Discount engine with flat/percent types, scope (product/cart), and caps. 

Subscription plans (recurring) or payment plans (finite payoff) per product (mutually exclusive). 

Rich search/filter/pagination; multi-channel flags; tax-exempt handling. 

Strong tenancy isolation; auditing; observability; performance-minded data access. 

Admin-level features for boundaries, alerts, templates, and internal controls. 

 

2) Goals & Non‑Goals 

Goals 

Authoritative catalog for pricing inputs and discount eligibility. 

Tenancy isolation by merchant with per-merchant configuration. 

Flexible read model for frontends via GraphQL; stable write and interservice contracts via REST. 

Clear demarcation of recurring (subscription) vs finite (payment plan) products with lifecycle semantics. 

Admin controls for boundaries, alert rules, message templates, and feature toggles. 

Non‑Goals 

Not processing payments or charging schedules (owned by Finance). 

Not storing PII beyond product metadata. 

Not a warehouse; analytics aggregations live elsewhere. 

 

3) Stakeholders & Integrations 

Merchants: create/manage catalog, discounts, plans, variants, categories. 

Customers: browse via merchant-facing frontend. 

Internal Admins: configure global/merchant-specific policies, templates, alerts, flags. 

Finance Service: consumes product/discount/plan data for pricing and collection; receives plan metadata for installments/subscriptions. 

Auth/Accounts: identity, roles, and merchant scoping. 

Partners/Marketplace: optional read access to catalog for integrations. 

Support: references products/discounts in tickets. 

 

4) Domain Model (Conceptual) 

... (previous entity breakdown remains the same) ... 

 

7) Subscription vs Payment Plan Semantics 

... (same as before) ... 

 

8) Admin-Specific Features 

The Admin Frontend UI supports configuration, overrides, and operational tooling for support and internal staff. 

8.1 Core Admin Objects 

MessageTemplate 

Fields: id, merchantId, key, channel (in-app/email/SMS), locale, body, variables[], version, status, publishedAt. 

Use cases: notification templates, pop-ups, banners. 

Workflow: draft → review → publish; supports preview and rollback. 

AlertRule 

Fields: id, merchantId, trigger, condition, severity, recipients[], throttle, enabled. 

Use cases: notify when discounts expire, products out of stock, or boundary violations occur. 

Workflow: create → test → enable; supports rate limiting. 

BoundaryPolicy 

Fields: id, merchantId, key, value, min, max, enforced, updatedBy. 

Use cases: enforce maximum discount %; cap payment plan terms; restrict late fees. 

Guards: service layer rejects inputs violating policy with POLICY_VIOLATION. 

FeatureToggle 

Fields: id, merchantId, flag, enabled, targetingJSON, rolloutPct, env. 

Use cases: enable payment plans for select merchants; beta feature rollout. 

AdminAuditLog 

Fields: id, actorId, actorRole, merchantId?, resourceType, resourceId, action, before, after, reason, at. 

Use cases: immutable record of all admin changes. 

8.2 Admin Workflows 

Assume Merchant Context: internal admin selects a merchant; UI displays banner to prevent cross-tenant confusion. 

Preview & Simulation: test templates with mock variables; simulate discounts or plan rules. 

Publish & Rollback: versioned changes with audit trail; scheduled publishes supported. 

8.3 Security & Scopes 

Roles: customer, merchant_user, merchant_admin, support_agent, internal_admin. 

Admin-only GraphQL namespace: admin { ... } with full CRUD on templates, alerts, policies. 

Separate endpoints possible (/graphql vs /admin-graphql) for stricter network control. 

 

9) Search, Filtering & Pagination 

... (same as before) ... 

 

18) Performance Considerations 

... (same as before) ... 

 

21) Dev Experience & Tooling 

Separate frontend builds: 

app.example.com (customer/merchant) 

admin.example.com (internal) 

Same backend; distinct GraphQL namespaces; strict RBAC. 

 

24) Open Questions 

... (includes Admin: Should boundary policies be globally defined or merchant-specific by default?) ... 

 

25) Phased Plan & Milestones 

Add Phase 6 – Admin Features: message templates, alert rules, boundaries, toggles, audit logs. 

 

27) Glossary (extended) 

MessageTemplate: reusable text block with variables and versions. 

AlertRule: condition-based notifications for support/admins. 

BoundaryPolicy: constraints applied at service level for safety. 

FeatureToggle: feature flag for merchants or environments. 

AdminAuditLog: record of internal/admin actions. 

 

Final Notes 

This guide now includes Admin Frontend capabilities for deeper customization, boundaries, and observability. Internal Admin is treated as a first-class client with its own GraphQL namespace, security model, and operational workflows. 

 

----------------------------------------- 
 

 
Admin Entities (Deferred) – Notes for Knowledge Base 

These entities and workflows are documented for future implementation and are not part of the initial (v1) build. Do not include them in the initial schema, migrations, or API contracts. 

Scope Status 

Status: Deferred 

Target Phase: Phase 6 – Admin Features 

Environments: Plan/design only; no runtime footprint in v1 

Entities (Do NOT implement in v1) 

MessageTemplate (deferred) 

Purpose: Centralize message content for in-app banners, pop-ups, email/SMS templates. 

Indicative Fields: id, merchantId, key, channel (in-app/email/SMS), locale, body, variables[], version, status, publishedAt. 

Workflows: draft → review → publish; preview with variable injection; rollback. 

AlertRule (deferred) 

Purpose: Declarative alerting for expiring discounts, boundary violations, etc. 

Indicative Fields: id, merchantId, trigger, condition, severity, recipients[], channels[], throttle, enabled. 

Workflows: create → test → enable; rate limiting; channel routing. 

BoundaryPolicy (deferred) 

Purpose: Hard safety limits (e.g., max discount %, payment plan term caps, late fee caps). 

Indicative Fields: id, merchantId, key, value, min, max, enforced, updatedBy. 

Behavior: Service-level guard rejects invalid inputs with POLICY_VIOLATION. 

FeatureToggle (deferred) 

Purpose: Per-merchant/segment feature flags and rollouts. 

Indicative Fields: id, merchantId, flag, enabled, targetingJSON, rolloutPct, env. 

AdminAuditLog (deferred) 

Purpose: Immutable record of admin changes for compliance and debugging. 

Indicative Fields: id, actorId, actorRole, merchantId?, resourceType, resourceId, action, before, after, reason, at. 

Admin Workflows (Deferred) 

Assume Merchant Context with clear UI banner. 

Preview & Simulation tools (templates, discounts, plan schedules). 

Versioned publish & rollback; scheduled publishes. 

Separate admin GraphQL namespace (admin { ... }) with strict RBAC. 

Notes for Future Implementation 

Maintain configuration precedence: global → partner → merchant → env override → emergency flag. 

Emit events: config.template.published, policy.boundary.updated, etc. 

Observability: dashboards for policy violations, failed publishes, most used templates. 

Acceptance Criteria for Phase 6 (when prioritized) 

CRUD for all admin entities in admin GraphQL namespace. 

Versioned MessageTemplate with preview and publication audit. 

BoundaryPolicy enforced across GraphQL + REST writes (server-side guard). 

AlertRule execution with throttling and notification fanout. 

AdminAuditLog written for all admin mutations. 

 