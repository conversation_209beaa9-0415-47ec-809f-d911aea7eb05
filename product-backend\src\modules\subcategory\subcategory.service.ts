import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateSubCategoryDto, UpdateSubCategoryDto } from './dto/subcategory.dto';
import { TransactionLogService } from '../transaction-log/transaction-log.service';

@Injectable()
export class SubCategoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly transactionLogService: TransactionLogService,
  ) {}

  async create(dto: CreateSubCategoryDto) {
    try {
      const created = await this.prisma.subCategory.create({ data: dto });
      // Fetch parent category to get merchantId
      const parentCategory = await this.prisma.category.findUnique({ where: { id: created.categoryId } });
      await this.transactionLogService.createLog({
        action: 'CREATE',
        entity: 'SubCategory',
        entityId: created.id,
        userId: parentCategory?.merchantId,
        newValue: created,
      });
      return created;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Subcategory name already exists for this category.');
      }
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to create subcategory'
      );
    }
  }

  async findAllByCategory(categoryId: string) {
    try {
      return await this.prisma.subCategory.findMany({ where: { categoryId } });
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to fetch subcategories'
      );
    }
  }

  async findOne(id: string, categoryId: string) {
    try {
      const subCategory = await this.prisma.subCategory.findUnique({ where: { id } });
      if (!subCategory) throw new NotFoundException('Subcategory not found');
      if (subCategory.categoryId !== categoryId) throw new BadRequestException('Category mismatch');
      return subCategory;
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to fetch subcategory'
      );
    }
  }

  async update(id: string, categoryId: string, dto: UpdateSubCategoryDto) {
    try {
      const before = await this.prisma.subCategory.findUnique({ where: { id } });
      if (!before) throw new NotFoundException('Subcategory not found');
      if (before.categoryId !== categoryId) throw new BadRequestException('Category mismatch');
      const updated = await this.prisma.subCategory.update({ where: { id }, data: dto });
      // Fetch parent category to get merchantId
      const parentCategory = await this.prisma.category.findUnique({ where: { id: updated.categoryId } });
      await this.transactionLogService.createLog({
        action: 'UPDATE',
        entity: 'SubCategory',
        entityId: updated.id,
        userId: parentCategory?.merchantId,
        oldValue: before,
        newValue: updated,
      });
      return updated;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Subcategory name already exists for this category.');
      }
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to update subcategory'
      );
    }
  }

  async remove(id: string, categoryId: string) {
    try {
      const before = await this.prisma.subCategory.findUnique({ where: { id } });
      if (!before) throw new NotFoundException('Subcategory not found');
      if (before.categoryId !== categoryId) throw new BadRequestException('Category mismatch');
      const deleted = await this.prisma.subCategory.delete({ where: { id } });
      // Fetch parent category to get merchantId
      const parentCategory = await this.prisma.category.findUnique({ where: { id: before.categoryId } });
      await this.transactionLogService.createLog({
        action: 'DELETE',
        entity: 'SubCategory',
        entityId: deleted.id,
        userId: parentCategory?.merchantId,
        oldValue: before,
      });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to delete subcategory'
      );
    }
  }
}