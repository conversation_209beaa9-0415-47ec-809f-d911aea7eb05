import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards, NotFoundException, Request, BadRequestException } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { TicketStatus, TicketPriority, Prisma } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { AuthenticatedRequest } from "../auth/src/types";
import { CreateTicketDto } from "./dto/create-ticket.dto";
import { UpdateTicketDto } from "./dto/update-ticket.dto";

import { TicketService } from "./ticket.service";
import { TicketPayload } from "./types/ticket.types";

@ApiTags("Tickets")
@Controller("tickets")
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class TicketController {
  constructor(private readonly ticketService: TicketService) {}

  @Post()
  @ApiOperation({ summary: "Create a new ticket" })
  @ApiResponse({ status: 201, description: "Ticket created successfully" })
  @ApiResponse({ status: 400, description: "Invalid input" })
  @ApiResponse({ status: 404, description: "Category not found" })
  @ApiResponse({ status: 500, description: "Internal server error" })
  async create(
    @Body() createTicketDto: CreateTicketDto,
    @Request() req: AuthenticatedRequest
  ): Promise<TicketPayload> {
    try {
      return await this.ticketService.create({
        ...createTicketDto,
        createdBy: req.user.id,
        lastUpdatedBy: req.user.id
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while creating the ticket: ${errorMessage}`
      );
    }
  }

  @Get()
  @ApiOperation({ summary: "Get all tickets" })
  @ApiQuery({ name: "skip", required: false, type: "number", description: "Number of items to skip" })
  @ApiQuery({ name: "take", required: false, type: "number", description: "Number of items to take" })
  @ApiQuery({ name: "status", required: false, enum: TicketStatus, description: "Filter by ticket status" })
  @ApiQuery({ name: "priority", required: false, enum: TicketPriority, description: "Filter by ticket priority" })
  async findAll(
    @Request() req: AuthenticatedRequest,
    @Query("skip") skip?: number,
    @Query("take") take?: number,
    @Query("status") status?: TicketStatus,
    @Query("priority") priority?: TicketPriority
  ): Promise<TicketPayload[]> {
    const where: Prisma.TicketWhereInput = {};
    if (status) {
      where.status = status;
    }
    if (priority) {
      where.priority = priority;
    }
    // Only show tickets created by the user unless they have an admin role
    if (!req.user.role.endsWith("_admin")) {
      where.createdBy = req.user.id;
    }
    const params: Prisma.TicketFindManyArgs = {
      skip,
      take,
      where
    };
    return this.ticketService.findMany(params);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get ticket by id" })
  @ApiResponse({ status: 200, description: "Ticket found" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async findOne(@Param("id") id: string): Promise<TicketPayload> {
    const ticket = await this.ticketService.findUnique({ id });
    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return ticket;
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a ticket" })
  @ApiResponse({ status: 200, description: "Ticket updated successfully" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async update(
    @Param("id") id: string,
    @Body() updateTicketDto: UpdateTicketDto,
    @Request() req: AuthenticatedRequest
  ): Promise<TicketPayload> {
    const result = await this.ticketService.update(
      { id },
      { ...updateTicketDto, lastUpdatedBy: req.user.id }
    );
    if (!result) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return result;
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a ticket" })
  @ApiResponse({ status: 200, description: "Ticket deleted successfully" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async remove(@Param("id") id: string): Promise<TicketPayload> {
    const result = await this.ticketService.delete({ id });
    if (!result) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return result;
  }
}
