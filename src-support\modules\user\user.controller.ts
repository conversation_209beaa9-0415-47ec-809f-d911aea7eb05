import { 
  <PERSON>, 
  Get, 
  Param,
  Query,
  UseGuards,
  Request,
  BadRequestException
} from '@nestjs/common';
import { 
  ApiBearerAuth, 
  ApiOperation, 
  ApiTags, 
  ApiQuery, 
  ApiResponse,
  ApiParam
} from '@nestjs/swagger';
import { ApiGuard } from '../auth/src/guards';
import { AuthenticatedRequest } from '../auth/src/types';
import { UserService } from './user.service';
import { GetUsersDto } from './dto/user.dto';
import { User, UserListResponse } from './user.model';

@ApiTags('Users')
@Controller('users')
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: 'Get all users from auth service' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'role', required: false, description: 'Filter by role', example: 'global_admin' })
  @ApiQuery({ name: 'active', required: false, description: 'Filter by active status', example: true })
  @ApiQuery({ name: 'search', required: false, description: 'Search by email or name', example: '<EMAIL>' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of users retrieved successfully',
    type: UserListResponse
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async getUsers(
    @Query() query: GetUsersDto,
    @Request() req: AuthenticatedRequest
  ): Promise<UserListResponse> {
    // Extract the JWT token from the request
    const authHeader = (req.headers as any)['authorization'] as string;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new BadRequestException('Authorization token is required');
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    return this.userService.getUsers(query, token);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID from auth service' })
  @ApiParam({ name: 'id', description: 'User ID', example: 'cmca6kix1000cpa3zn3c7f21c' })
  @ApiResponse({ 
    status: 200, 
    description: 'User retrieved successfully',
    type: User
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest
  ): Promise<User> {
    // Extract the JWT token from the request
    const authHeader = (req.headers as any)['authorization'] as string;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new BadRequestException('Authorization token is required');
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    return this.userService.getUserById(id, token);
  }

  @Get('role/:role')
  @ApiOperation({ summary: 'Get users by role from auth service' })
  @ApiParam({ name: 'role', description: 'User role', example: 'global_admin' })
  @ApiResponse({ 
    status: 200, 
    description: 'Users retrieved successfully',
    type: [User]
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async getUsersByRole(
    @Param('role') role: string,
    @Request() req: AuthenticatedRequest
  ): Promise<User[]> {
    // Extract the JWT token from the request
    const authHeader = (req.headers as any)['authorization'] as string;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new BadRequestException('Authorization token is required');
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    return this.userService.getUsersByRole(role, token);
  }

  @Get('status/active')
  @ApiOperation({ summary: 'Get all active users from auth service' })
  @ApiResponse({ 
    status: 200, 
    description: 'Active users retrieved successfully',
    type: [User]
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async getActiveUsers(
    @Request() req: AuthenticatedRequest
  ): Promise<User[]> {
    // Extract the JWT token from the request
    const authHeader = (req.headers as any)['authorization'] as string;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new BadRequestException('Authorization token is required');
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    return this.userService.getActiveUsers(token);
  }
}
