import { InputType, Field, registerEnumType } from "@nestjs/graphql";
import { ApiProperty } from "@nestjs/swagger";
import { TicketPriority as PrismaTicketPriority } from "@prisma/client";
import { IsString, IsEnum, IsUUID, IsArray, IsOptional } from "class-validator";

registerEnumType(PrismaTicketPriority, {
  name: "TicketPriority",
  description: "The priority of the ticket",
});

@InputType()
export class CreateTicketDto {
  @ApiProperty({ description: "Ticket subject" })
  @IsString()
  @Field()
  subject: string;

  @ApiProperty({ description: "Ticket description" })
  @IsString()
  @Field()
  description: string;

  @ApiProperty({ description: "Category ID" })
  @IsUUID()
  @Field()
  categoryId: string;

  @ApiProperty({ description: "Ticket priority", enum: PrismaTicketPriority })
  @IsEnum(PrismaTicketPriority)
  @Field(() => PrismaTicketPriority)
  priority: PrismaTicketPriority;

  @ApiProperty({ description: "Account ID", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  accountId?: string;

  @ApiProperty({ description: "Partner ID", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  partnerId?: string;

  @ApiProperty({ description: "Users to assign the ticket to", required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Field(() => [String], { nullable: true })
  assignedTo?: string[];
}
