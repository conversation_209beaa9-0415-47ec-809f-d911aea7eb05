{"info": {"name": "Product Backend API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3070/api/v1"}, {"key": "merchantId", "value": "YOUR_MERCHANT_ID"}, {"key": "categoryId", "value": "YOUR_CATEGORY_ID"}, {"key": "subCategoryId", "value": "YOUR_SUBCATEGORY_ID"}, {"key": "productId", "value": "YOUR_PRODUCT_ID"}, {"key": "variantId", "value": "YOUR_VARIANT_ID"}], "item": [{"name": "Product", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["products"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Get Single Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sample Product\",\n  \"sku\": \"SKU12345\",\n  \"categoryId\": \"{{categoryId}}\",\n  \"merchantId\": \"{{merchantId}}\",\n  \"price\": 100,\n  \"discount\": 10,\n  \"subCategoryId\": \"{{subCategoryId}}\",\n  \"brand\": \"BrandX\",\n  \"itemWeight\": \"1kg\",\n  \"length\": \"10cm\",\n  \"width\": \"5cm\",\n  \"description\": \"A sample product\",\n  \"isInStore\": true,\n  \"isOnline\": true,\n  \"productImages\": [],\n  \"productStatus\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}}}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"price\": 200,\n  \"discount\": 15,\n  \"sku\": \"SKU-UPDATED-001\",\n  \"categoryId\": \"{{categoryId}}\",\n  \"subCategoryId\": \"{{subCategoryId}}\",\n  \"brand\": \"UpdatedBrand\",\n  \"itemWeight\": \"2kg\",\n  \"length\": \"20cm\",\n  \"width\": \"10cm\",\n  \"description\": \"This is an updated product description.\",\n  \"isInStore\": false,\n  \"isOnline\": true,\n  \"productImages\": [],\n  \"productStatus\": \"inactive\"\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}]}, {"name": "Category", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["categories"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Get Single Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\",\n  \"merchantId\": \"{{merchantId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/categories", "host": ["{{baseUrl}}"], "path": ["categories"]}}}, {"name": "Update Category", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Category Name\"\n}"}, "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}?merchantId={{merchantId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}"], "query": [{"key": "merchantId", "value": "{{merchantId}}"}]}}}]}, {"name": "Subcategory", "item": [{"name": "Get All Subcategories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}/subcategories", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}", "subcategories"]}}}, {"name": "Get Single Subcategory", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}/subcategories/{{subCategoryId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}", "subcategories", "{{subCategoryId}}"]}}}, {"name": "Create Subcategory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}/subcategories", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}", "subcategories"]}}}, {"name": "Update Subcategory", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Subcategory Name\"\n}"}, "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}/subcategories/{{subCategoryId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}", "subcategories", "{{subCategoryId}}"]}}}, {"name": "Delete Subcategory", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/categories/{{categoryId}}/subcategories/{{subCategoryId}}", "host": ["{{baseUrl}}"], "path": ["categories", "{{categoryId}}", "subcategories", "{{subCategoryId}}"]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Get All Variants", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/variants", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "variants"]}}}, {"name": "Get Single Variant", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/variants/{{variantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "variants", "{{variantId}}"]}}}, {"name": "C<PERSON> <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Large Size\",\n  \"sku\": \"SKU-12345-L\",\n  \"price\": 150,\n  \"attributes\": {\n    \"color\": \"Red\",\n    \"size\": \"Large\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}/variants", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "variants"]}}}, {"name": "Update <PERSON><PERSON><PERSON>", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Variant Name\",\n  \"sku\": \"SKU-12345-XL\",\n  \"price\": 200,\n  \"attributes\": {\n    \"color\": \"Blue\",\n    \"size\": \"XLarge\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}/variants/{{variantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "variants", "{{variantId}}"]}}}, {"name": "Delete Variant", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/variants/{{variantId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "variants", "{{variantId}}"]}}}]}]}