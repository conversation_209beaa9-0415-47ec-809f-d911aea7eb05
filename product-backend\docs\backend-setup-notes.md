# Product Backend Setup & Development Notes

## Environment & Tooling
- Docker Compose is used for local Postgres, Redis, and pgAdmin.
- Prisma ORM is used for database migrations and schema management.
- NestJS (with Fastify) is the backend framework.
- TypeScript is configured with strict mode, experimentalDecorators, and emitDecoratorMetadata enabled.
- Environment variables are managed in `.env` (local) and should be mirrored in `.env.example` for team onboarding.

## Database & Prisma
- Postgres container is configured with user: `pg-admin`, password: `localpassword`, db: `product-db`.
- Prisma schema uses string IDs (`cuid()`) for Product and related models for global uniqueness and future scalability.
- **Database tables and relations have been updated to enforce foreign key constraints.**  
  - For example, `Product.categoryId` must reference an existing `Category.id`.
  - Attempting to insert a `Product` with a non-existent `categoryId` will fail.
- **Category and SubCategory tables must be seeded before inserting Products.**
- Prisma migrations are run with `npx prisma migrate dev --name <migration-name>`.
- PrismaService is implemented for NestJS integration, with shutdown hooks and error suppression for 'beforeExit'.

## Product Model Field Descriptions
- **id**: Unique string identifier for each product (cuid).
- **name**: Name of the product.
- **price**: Price of the product (integer, optional).
- **discount**: Discount amount or percentage (integer, optional).
- **productStatus**: Status of the product (e.g., available, out of stock).
- **taxExempt**: Boolean indicating if the product is exempt from tax.
- **sku**: Stock Keeping Unit, a unique code for tracking products in inventory.
- **categoryId**: Foreign key referencing the product's category.
- **subCategoryId**: Foreign key referencing the product's subcategory.
- **brand**: Brand name of the product.
- **itemWeight**: Weight of the product (string).
- **length, breadth, width**: Dimensions of the product (strings).
- **description**: Description of the product.
- **isInStore**: Boolean indicating if the product is available in-store.
- **isOnline**: Boolean indicating if the product is available online.
- **productImages**: JSON field for storing product images.
- **createdAt**: Timestamp when the product was created.
- **deletedAt**: Timestamp for soft deletion (optional).
- **merchantID**: Identifier for the merchant who owns the product.
- **referenceID**: External reference ID (optional).
- **referenceSource**: Source of the external reference (optional).
- **variants**: Array of related Variant models.
- **category**: Relation to Category model.
- **subCategory**: Relation to SubCategory model.

## DTOs & Validation
- DTOs (Data Transfer Objects) are used for input validation and type safety in NestJS controllers.
- `class-validator` and `@nestjs/mapped-types` are installed for validation and partial DTOs.
- DTOs are updated to match the Prisma Product model, including all required and optional fields.

## Product Module
- Product module, service, and controller are scaffolded in `src/modules/product`.
- CRUD methods are implemented in the service, using Prisma for database access.
- All Product lookups use `id: string` to match the Prisma schema.

## ID Strategy
- String IDs (`cuid`) are used for global uniqueness, security, and distributed system compatibility.
- If other microservices use numeric IDs, store them as a separate field (e.g., `externalId`) for integration.
- Changing ID type later is possible but requires coordinated migration and code updates.

## Recommendations
- Stick with string IDs for new features unless the team standardizes on numeric IDs.
- Document all environment variables and onboarding steps for new developers.
- Revisit ID strategy with the team before production if unsure.

## Recent Schema/Table Changes

- **Foreign key constraints** are strictly enforced between `Product`, `Category`, and `SubCategory`.
- **Seed order matters:** Categories and subcategories must exist before products referencing them are inserted.
- **Error handling:** Inserting a product with a missing category or subcategory will result in a foreign key violation.
- **Database initialization:** Use `npx prisma migrate reset` to reset and re-seed the database for a clean state during development.

---

_Last updated: August 20, 2025_
