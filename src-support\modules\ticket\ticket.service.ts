import { Injectable } from "@nestjs/common";
import { Prisma, TicketPriority } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import {
  TicketPayload,
  ticketInclude
} from "./types/ticket.types";

export interface CreateTicketParams {
  subject: string;
  description: string;
  priority: TicketPriority;
  categoryId: string;
  accountId?: string;
  partnerId?: string;
  assignedTo?: string[];
  createdBy: string;
  lastUpdatedBy: string;
}

@Injectable()
export class TicketService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateTicketParams): Promise<TicketPayload> {
    console.log('TicketService.create received data:', data);

    const createInput: Prisma.TicketCreateInput = {
      subject: data.subject,
      description: data.description,
      priority: data.priority,
      category: {
        connect: { id: data.categoryId }
      },
      status: "OPEN",
      createdBy: data.createdBy,
      lastUpdatedBy: data.lastUpdatedBy,
      ...(data.accountId && {
        accountId: data.accountId
      }),
      ...(data.partnerId && {
        partnerId: data.partnerId
      }),
      ...(data.assignedTo?.length && {
        assignedTo: data.assignedTo
      })
    };

    return this.prisma.ticket.create({
      data: createInput,
      include: ticketInclude
    });
  }

  async findUnique(where: Prisma.TicketWhereUniqueInput): Promise<TicketPayload | null> {
    return this.prisma.ticket.findUnique({
      where,
      include: ticketInclude
    });
  }

  async findMany(params: {
    skip?: number;
    take?: number;
    where?: Prisma.TicketWhereInput;
    orderBy?: Prisma.TicketOrderByWithRelationInput | Prisma.TicketOrderByWithRelationInput[];
  } = {}): Promise<TicketPayload[]> {
    const { skip, take, where, orderBy } = params;
    return this.prisma.ticket.findMany({
      skip,
      take,
      where,
      orderBy: orderBy || { createdAt: "desc" },
      include: ticketInclude
    });
  }

  async update(
    where: Prisma.TicketWhereUniqueInput,
    data: Prisma.TicketUpdateInput
  ): Promise<TicketPayload> {
    return this.prisma.ticket.update({
      where,
      data,
      include: ticketInclude
    });
  }

  async delete(where: Prisma.TicketWhereUniqueInput): Promise<TicketPayload> {
    return this.prisma.ticket.delete({
      where,
      include: ticketInclude
    });
  }
}
