import {
  Controller,
  Get,
  Query,
  BadRequestException,
  Post,
  Body,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { CategoryService } from './category.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Categories')
@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  @ApiOperation({ summary: 'Get all categories for a merchant' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  async findAll(@Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.findAllByMerchant(merchantId);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiBody({ type: CreateCategoryDto })
  @ApiResponse({ status: 201, description: 'Category created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data' })
  create(@Body() dto: CreateCategoryDto) {
    return this.categoryService.create(dto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Category retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  findOne(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.findOne(id, merchantId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiBody({ type: UpdateCategoryDto })
  @ApiResponse({ status: 200, description: 'Category updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  update(
    @Param('id') id: string,
    @Query('merchantId') merchantId: string,
    @Body() dto: UpdateCategoryDto,
  ) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.update(id, merchantId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Category soft deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  remove(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.remove(id, merchantId);
  }

  @Get('deleted/list')
  @ApiOperation({ summary: 'Get all soft deleted categories for a merchant' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Deleted categories retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  async findDeleted(@Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.findDeletedByMerchant(merchantId);
  }

  @Patch(':id/restore')
  @ApiOperation({ summary: 'Restore a soft deleted category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Category restored successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  restore(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.restoreCategory(id, merchantId);
  }

  @Delete(':id/permanent')
  @ApiOperation({ summary: 'Permanently delete a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Category permanently deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required or category not soft deleted' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  permanentDelete(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.categoryService.permanentDeleteCategory(id, merchantId);
  }
}