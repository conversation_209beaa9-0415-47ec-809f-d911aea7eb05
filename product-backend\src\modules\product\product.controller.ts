import { Controller, Get, Post, Body, Param, Patch, Delete, Query, BadRequestException, Req, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiConsumes, ApiBody } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';
import { UploadProductImagesResponseDto } from './dto/upload-product-images.dto';
import { ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Products')
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  // Fastify-compatible upload endpoint for multiple product images
  @Post('upload-images')
  @HttpCode(201)
  @ApiOperation({ summary: 'Upload multiple product images' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Images uploaded successfully', type: UploadProductImagesResponseDto })
  @ApiResponse({ status: 400, description: 'No images uploaded' })
  async uploadProductImages(@Req() req: any): Promise<UploadProductImagesResponseDto> {
    // Ensure uploads directory exists
    const uploadDir = path.join(process.cwd(), 'uploads', 'product-images');
    fs.mkdirSync(uploadDir, { recursive: true });

    // Parse multipart
    const parts = req.parts();
    const imageUrls: string[] = [];
    for await (const part of parts) {
      if (part.file) {
        const ext = path.extname(part.filename);
        const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1e9)}${ext}`;
        const filePath = path.join(uploadDir, uniqueName);
        const writeStream = fs.createWriteStream(filePath);
        await new Promise((resolve, reject) => {
          part.file.pipe(writeStream);
          part.file.on('end', resolve);
          part.file.on('error', reject);
        });
        imageUrls.push(`/uploads/product-images/${uniqueName}`);
      }
    }
    if (imageUrls.length === 0) {
      throw new BadRequestException('No images uploaded');
    }
    return { imageUrls };
  }

  //create a product
  @Post()
  @ApiOperation({
    summary: 'Create a new product',
    description: 'Creates a new product with the provided details. Requires merchantId and categoryId.'
  })
  @ApiBody({
    type: CreateProductDto,
    examples: {
      'electronics-product': {
        summary: 'Electronics Product Example',
        value: {
          name: 'Premium Wireless Headphones',
          price: 2999,
          sku: 'PWH-001-BLK',
          categoryId: 'cmfl41swn000brv01hlxhrwa8',
          subCategoryId: 'cmfl41swn000erv01h9963wi4',
          brand: 'TechSound Pro',
          description: 'High-quality wireless headphones with noise cancellation, 30-hour battery life, and premium sound quality.',
          isInStore: true,
          isOnline: true,
          productImages: [
            'https://example.com/images/headphones-front.jpg',
            'https://example.com/images/headphones-side.jpg'
          ],
          merchantId: 'merchant_12345',
          productStatus: 'ACTIVE',
          count: 150,
          saleType: 'REGULAR'
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Product created successfully',
    schema: {
      example: {
        id: 'prod_67890',
        name: 'Premium Wireless Headphones',
        price: 2999,
        sku: 'PWH-001-BLK',
        categoryId: 'cmfl41swn000brv01hlxhrwa8',
        subCategoryId: 'cmfl41swn000erv01h9963wi4',
        brand: 'TechSound Pro',
        description: 'High-quality wireless headphones with noise cancellation...',
        isInStore: true,
        isOnline: true,
        productImages: ['https://example.com/images/headphones-front.jpg'],
        merchantId: 'merchant_12345',
        productStatus: 'ACTIVE',
        count: 150,
        saleType: 'REGULAR',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - missing required fields',
    schema: {
      example: {
        statusCode: 400,
        message: 'merchantId is required',
        error: 'Bad Request'
      }
    }
  })
  async create(@Body() dto: CreateProductDto) {
    if (!dto.merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    if (!dto.categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.productService.create(dto);
  }

  //get all products
  @Get()
  @ApiOperation({
    summary: 'Get all products for a merchant',
    description: 'Retrieves all products belonging to the specified merchant. Returns an array of product objects.'
  })
  @ApiQuery({
    name: 'merchantId',
    required: true,
    description: 'Merchant ID to filter products',
    example: 'merchant_12345'
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    schema: {
      example: [
        {
          id: 'prod_67890',
          name: 'Premium Wireless Headphones',
          price: 2999,
          sku: 'PWH-001-BLK',
          categoryId: 'cat_electronics_001',
          subCategoryId: 'subcat_audio_001',
          brand: 'TechSound Pro',
          description: 'High-quality wireless headphones...',
          isInStore: true,
          isOnline: true,
          productImages: ['https://example.com/images/headphones-front.jpg'],
          merchantId: 'merchant_12345',
          productStatus: 'ACTIVE',
          count: 150,
          saleType: 'REGULAR',
          createdAt: '2024-01-15T10:30:00.000Z',
          updatedAt: '2024-01-15T10:30:00.000Z'
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - merchantId is required',
    schema: {
      example: {
        statusCode: 400,
        message: 'merchantId is required',
        error: 'Bad Request'
      }
    }
  })
  findAll(@Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.productService.findAll(merchantId);
  }

  //get one product
  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Product retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  findOne(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.productService.findOne(id, merchantId);
  }

  //update a product
  @Patch(':id')
  @ApiOperation({ summary: 'Update a product' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiBody({ type: UpdateProductDto })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  update(
    @Param('id') id: string,
    @Query('merchantId') merchantId: string,
    @Body() dto: UpdateProductDto
  ) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.productService.update(id, merchantId, dto);
  }

  //delete a product
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a product' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  remove(@Param('id') id: string, @Query('merchantId') merchantId: string) {
    if (!merchantId) throw new BadRequestException('merchantId is required');
    return this.productService.remove(id, merchantId);
  }
}