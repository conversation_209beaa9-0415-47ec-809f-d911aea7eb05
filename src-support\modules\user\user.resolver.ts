import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards, BadRequestException } from '@nestjs/common';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { UserService } from './user.service';
import { GetUsersDto } from './dto/user.dto';
import { User, UserListResponse } from './user.model';

@Resolver(() => User)
@UseGuards(GraphQLAuthGuard)
export class UserResolver {
  constructor(private readonly userService: UserService) {}

  @Query(() => UserListResponse, { name: 'users' })
  async getUsers(
    @Args('params', { nullable: true }) params: GetUsersDto = {},
    @Context() context: any
  ): Promise<UserListResponse> {
    // Extract cookies from the GraphQL context (same as AuthResolver pattern)
    const request = context.req;
    const cookies = request.cookies || {};

    // Get the raw access token from cookies for forwarding to external service
    const accessToken = cookies['access_token'];
    if (!accessToken) {
      throw new BadRequestException('Access token not found in cookies');
    }

    return this.userService.getUsers(params, accessToken);
  }

  @Query(() => User, { name: 'user' })
  async getUserById(
    @Args('id') id: string,
    @Context() context: any
  ): Promise<User> {
    // Extract cookies from the GraphQL context
    const request = context.req;
    const cookies = request.cookies || {};

    // Get the raw access token from cookies for forwarding to external service
    const accessToken = cookies['access_token'];
    if (!accessToken) {
      throw new BadRequestException('Access token not found in cookies');
    }

    return this.userService.getUserById(id, accessToken);
  }

  @Query(() => [User], { name: 'usersByRole' })
  async getUsersByRole(
    @Args('role') role: string,
    @Context() context: any
  ): Promise<User[]> {
    // Extract cookies from the GraphQL context
    const request = context.req;
    const cookies = request.cookies || {};

    // Get the raw access token from cookies for forwarding to external service
    const accessToken = cookies['access_token'];
    if (!accessToken) {
      throw new BadRequestException('Access token not found in cookies');
    }

    return this.userService.getUsersByRole(role, accessToken);
  }

  @Query(() => [User], { name: 'activeUsers' })
  async getActiveUsers(
    @Context() context: any
  ): Promise<User[]> {
    // Extract cookies from the GraphQL context
    const request = context.req;
    const cookies = request.cookies || {};

    // Get the raw access token from cookies for forwarding to external service
    const accessToken = cookies['access_token'];
    if (!accessToken) {
      throw new BadRequestException('Access token not found in cookies');
    }

    return this.userService.getActiveUsers(accessToken);
  }
}
