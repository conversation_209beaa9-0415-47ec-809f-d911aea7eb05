import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Reflector } from '@nestjs/core';
import { AuthSharedService } from '../auth.service';

@Injectable()
export class GraphQLAuthGuard implements CanActivate {
  private readonly logger = new Logger(GraphQLAuthGuard.name);

  constructor(
    private readonly authService: AuthSharedService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Get GraphQL context
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;

    if (!request) {
      this.logger.error('GraphQL request context not found');
      return false;
    }

    try {
      // Extract cookies from request
      const cookies = request.cookies || {};
      this.logger.log('GraphQL Auth Guard: Authenticating request');

      // Authenticate using cookies
      const user = await this.authService.authenticateFromCookies(cookies);
      
      // Attach user to request context for use in resolvers
      request.user = user;
      
      this.logger.log(`GraphQL authentication successful for user: ${user.email}`);
      return true;
    } catch (error) {
      this.logger.error('GraphQL authentication failed:', error.message);
      return false;
    }
  }
}
