import { Controller, Post, Body, Get, Param, Patch, Delete, BadRequestException, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { SubscriptionPlanService } from './subscription-plan.service';
import { CreateSubscriptionPlanDto } from './dto/create-subscription-plan.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Subscription Plans')
@Controller('subscription-plans')
export class SubscriptionPlanController {
	constructor(private readonly subscriptionPlanService: SubscriptionPlanService) {}

	// When JWT is implemented, merchantId will be extracted from req.user
	@Post()
	@ApiOperation({ summary: 'Create a new subscription plan' })
	@ApiBody({ type: CreateSubscriptionPlanDto })
	@ApiResponse({ status: 201, description: 'Subscription plan created successfully' })
	@ApiResponse({ status: 400, description: 'Bad request - productId or merchantId is required' })
	async create(@Body() dto: CreateSubscriptionPlanDto, @Req() req: any) {
		const merchantId = req.user?.merchantId; // Will work after JWT implementation
		if (!dto.productId) throw new BadRequestException('productId is required');
		if (!merchantId) throw new BadRequestException('merchantId is required');
		return this.subscriptionPlanService.create(dto, merchantId);
	}

	@Get(':id')
	@ApiOperation({ summary: 'Get a subscription plan by ID' })
	@ApiParam({ name: 'id', description: 'Subscription plan ID' })
	@ApiResponse({ status: 200, description: 'Subscription plan retrieved successfully' })
	@ApiResponse({ status: 404, description: 'Subscription plan not found' })
	async findOne(@Param('id') id: string) {
		return this.subscriptionPlanService.findOne(id);
	}

	@Get()
	@ApiOperation({ summary: 'Get all subscription plans' })
	@ApiResponse({ status: 200, description: 'Subscription plans retrieved successfully' })
	async findAll() {
		return this.subscriptionPlanService.findAll();
	}

	// When JWT is implemented, merchantId will be extracted from req.user
	@Patch(':id')
	@ApiOperation({ summary: 'Update a subscription plan' })
	@ApiParam({ name: 'id', description: 'Subscription plan ID' })
	@ApiBody({ type: CreateSubscriptionPlanDto })
	@ApiResponse({ status: 200, description: 'Subscription plan updated successfully' })
	@ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
	@ApiResponse({ status: 404, description: 'Subscription plan not found' })
	async update(
		@Param('id') id: string,
		@Body() dto: Partial<CreateSubscriptionPlanDto>,
		@Req() req: any
	) {
		const merchantId = req.user?.merchantId; // Will work after JWT implementation
		if (!merchantId) throw new BadRequestException('merchantId is required');
		return this.subscriptionPlanService.update(id, dto, merchantId);
	}

	// When JWT is implemented, merchantId will be extracted from req.user
	@Delete(':id')
	@ApiOperation({ summary: 'Delete a subscription plan' })
	@ApiParam({ name: 'id', description: 'Subscription plan ID' })
	@ApiResponse({ status: 200, description: 'Subscription plan deleted successfully' })
	@ApiResponse({ status: 400, description: 'Bad request - merchantId is required' })
	@ApiResponse({ status: 404, description: 'Subscription plan not found' })
	async remove(@Param('id') id: string, @Req() req: any) {
		const merchantId = req.user?.merchantId; // Will work after JWT implementation
		if (!merchantId) throw new BadRequestException('merchantId is required');
		return this.subscriptionPlanService.remove(id, merchantId);
	}
}
