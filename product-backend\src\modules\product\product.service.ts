import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Prisma } from '@prisma/client';
import { TransactionLogService } from '../transaction-log/transaction-log.service';
import { BaseSoftDeleteService } from '../../common/base-soft-delete.service';

@Injectable()
export class ProductService extends BaseSoftDeleteService {
  constructor(
    protected readonly prisma: PrismaService,
    private readonly transactionLogService: TransactionLogService,
  ) {
    super(prisma);
  }

  protected getModelName(): string {
    return 'Product';
  }

  protected getModel() {
    return this.prisma.product;
  }

  async create(dto: CreateProductDto) {
    if (dto.discountIds && dto.discountIds.length > 0) {
      const discounts = await this.prisma.discount.findMany({
        where: {
          id: { in: dto.discountIds },
        },
        select: { id: true, merchantId: true },
      });
      const invalid = discounts.some((d) => d.merchantId !== dto.merchantId);
      if (invalid || discounts.length !== dto.discountIds.length) {
        throw new BadRequestException('One or more discounts are invalid or do not belong to this merchant.');
      }
    }


    // Validate discountIds belong to the same merchant
    if (dto.discountIds && dto.discountIds.length > 0) {
      const discounts = await this.prisma.discount.findMany({
        where: {
          id: { in: dto.discountIds },
        },
        select: { id: true, merchantId: true },
      });
      const invalid = discounts.some((d) => d.merchantId !== dto.merchantId);
      if (invalid || discounts.length !== dto.discountIds.length) {
        throw new BadRequestException('One or more discounts are invalid or do not belong to this merchant.');
      }
    }
    // Ensure productImages is always an array
    let productImages: string[] | undefined = undefined;
    if (dto.productImages) {
      if (Array.isArray(dto.productImages)) {
        productImages = dto.productImages;
      } else if (typeof dto.productImages === 'string') {
        productImages = [dto.productImages];
      }
    }
    const data: Prisma.ProductCreateInput = {
      name: dto.name,
      price: dto.price,
      count: dto.count ?? 0,
      sku: dto.sku,
      category: { connect: { id: dto.categoryId } },
      subCategory: dto.subCategoryId ? { connect: { id: dto.subCategoryId } } : undefined,
      brand: dto.brand,
      itemWeight: dto.itemWeight,
      length: dto.length,
      width: dto.width,
      description: dto.description,
      isInStore: dto.isInStore,
      isOnline: dto.isOnline,
      productImages,
      createdAt: dto.createdAt ? new Date(dto.createdAt) : undefined,
      deletedAt: dto.deletedAt ? new Date(dto.deletedAt) : undefined,
      merchant: { connect: { id: dto.merchantId } },
      productStatus: dto.productStatus,
      saleType: dto.saleType,
      discounts: dto.discountIds && dto.discountIds.length > 0 ? {
        connect: dto.discountIds.map((id) => ({ id }))
      } : undefined,
    };
    try {
      const created = await this.prisma.product.create({ data });
      // Audit log
      await this.transactionLogService.createLog({
        action: 'CREATE',
        entity: 'Product',
        entityId: created.id,
        userId: dto.merchantId,
        newValue: created,
      });
      return created;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Unique constraint failed (e.g., SKU already exists for this merchant).');
      }
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to create product'
      );
    }
  }

  async findAll(merchantId: string) {
    try {
      return await this.prisma.product.findMany({
        where: {
          merchantId,
          deletedAt: null
        },
        include: {
          category: true,
          subCategory: true,
          merchant: true,
          variants: {
            where: { deletedAt: null }
          },
          discounts: {
            where: { deletedAt: null }
          },
          subscriptionPlans: true,
          paymentPlans: true,
        },
      });
    } catch (error) {
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to fetch products'
      );
    }
  }

  async findOne(id: string, merchantId: string) {
    try {
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          category: true,
          subCategory: true,
          merchant: true,
          variants: {
            where: { deletedAt: null }
          },
          discounts: {
            where: { deletedAt: null }
          },
          subscriptionPlans: true,
          paymentPlans: true,
        },
      });
      if (!product) throw new NotFoundException('Product not found');
      if (product.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');
      return product;
    } catch (error) {
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to fetch product'
      );
    }
  }

  async update(id: string, merchantId: string, dto: UpdateProductDto) {
    try {
      const product = await this.prisma.product.findUnique({ where: { id } });
      if (!product) throw new NotFoundException('Product not found');
      if (product.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      // Validate discountIds belong to the same merchant
      if (dto.discountIds && dto.discountIds.length > 0) {
        const discounts = await this.prisma.discount.findMany({
          where: {
            id: { in: dto.discountIds },
          },
          select: { id: true, merchantId: true },
        });
        const invalid = discounts.some((d) => d.merchantId !== merchantId);
        if (invalid || discounts.length !== dto.discountIds.length) {
          throw new BadRequestException('One or more discounts are invalid or do not belong to this merchant.');
        }
      }

      // Ensure productImages is always an array
      let productImages: string[] | undefined = undefined;
      if (dto.productImages) {
        if (Array.isArray(dto.productImages)) {
          productImages = dto.productImages;
        } else if (typeof dto.productImages === 'string') {
          productImages = [dto.productImages];
        }
      }
      const data: Prisma.ProductUpdateInput = {
        name: dto.name,
        price: dto.price,
        count: dto.count,
        sku: dto.sku,
        category: dto.categoryId ? { connect: { id: dto.categoryId } } : undefined,
        subCategory: dto.subCategoryId ? { connect: { id: dto.subCategoryId } } : undefined,
        brand: dto.brand,
        itemWeight: dto.itemWeight,
        length: dto.length,
        width: dto.width,
        description: dto.description,
        isInStore: dto.isInStore,
        isOnline: dto.isOnline,
        productImages,
        createdAt: dto.createdAt ? new Date(dto.createdAt) : undefined,
        deletedAt: dto.deletedAt ? new Date(dto.deletedAt) : undefined,
        merchant: dto.merchantId ? { connect: { id: dto.merchantId } } : undefined,
        productStatus: dto.productStatus,
        saleType: dto.saleType,
        discounts: dto.discountIds
          ? { set: dto.discountIds.map((id) => ({ id })) }
          : undefined,
      };

      (Object.keys(data) as Array<keyof Prisma.ProductUpdateInput>).forEach(
        (key) => data[key] === undefined && delete data[key]
      );

      if (Object.keys(data).length === 0) {
        throw new BadRequestException('No valid fields provided for update.');
      }

      const before = await this.prisma.product.findUnique({ where: { id } });
      const updated = await this.prisma.product.update({ where: { id }, data });
      // Audit log
      await this.transactionLogService.createLog({
        action: 'UPDATE',
        entity: 'Product',
        entityId: updated.id,
        userId: merchantId,
        oldValue: before,
        newValue: updated,
      });
      return updated;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Unique constraint failed (e.g., SKU already exists for this merchant).');
      }
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to update product'
      );
    }
  }

  async remove(id: string, merchantId: string) {
    try {
      const product = await this.prisma.product.findUnique({ where: { id } });
      if (!product) throw new NotFoundException('Product not found');
      if (product.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      // Soft delete the product
      const deleted = await this.softDelete(id, merchantId);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'SOFT_DELETE',
        entity: 'Product',
        entityId: deleted.id,
        userId: merchantId,
        oldValue: product,
      });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to delete product'
      );
    }
  }

  // New methods for soft delete management
  async findDeletedByMerchant(merchantId: string) {
    return this.findAllDeleted(
      { merchantId },
      {
        category: true,
        subCategory: true,
        merchant: true,
        variants: true,
        discounts: true,
        subscriptionPlans: true,
        paymentPlans: true,
      }
    );
  }

  async restoreProduct(id: string, merchantId: string) {
    try {
      const product = await this.prisma.product.findUnique({ where: { id } });
      if (!product) throw new NotFoundException('Product not found');
      if (product.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      const restored = await this.restore(id);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'RESTORE',
        entity: 'Product',
        entityId: restored.id,
        userId: merchantId,
        newValue: restored,
      });
      return restored;
    } catch (error) {
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to restore product'
      );
    }
  }

  async permanentDeleteProduct(id: string, merchantId: string) {
    try {
      const product = await this.prisma.product.findUnique({ where: { id } });
      if (!product) throw new NotFoundException('Product not found');
      if (product.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      const deleted = await this.permanentDelete(id);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'PERMANENT_DELETE',
        entity: 'Product',
        entityId: deleted.id,
        userId: merchantId,
        oldValue: product,
      });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as any).message
          : 'Failed to permanently delete product'
      );
    }
  }
}