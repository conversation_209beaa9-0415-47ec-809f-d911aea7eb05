import { Injectable } from "@nestjs/common";

export type HealthAccessType = "public" | "user" | "admin";

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  access: HealthAccessType;
  details?: {
    database?: boolean;
    cache?: boolean;
    auth?: boolean;
  };
  message?: string;
}

@Injectable()
export class HealthService {
  checkHealth(accessType: HealthAccessType): HealthCheckResponse {
    // Here you could add real health checks for different components
    const details = {
      database: true,
      cache: true,
      auth: true
    };

    const messages = {
      public: "Basic health check",
      user: "Authenticated health check",
      admin: "Full system health check with details"
    };

    return {
      status: "ok",
      timestamp: new Date().toISOString(),
      access: accessType,
      details: accessType === "admin" ? details : undefined,
      message: messages[accessType]
    };
  }
}
