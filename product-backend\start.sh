#!/bin/sh

echo "🟡 Starting Prisma migration setup..."

# Ensure build exists
if [ ! -f dist/main.js ]; then
  echo "❌ Build not found. Did you forget to run 'npm run build'?"
  exit 1
fi

# # # Parse a new URL with 'postgres' as the database name for admin connection
 ADMIN_DB_URL=$(echo "$DATABASE_URL" | sed -E 's|/[^/]+$|/postgres|')

# # # Extract target DB name from DATABASE_URL
 TARGET_DB=$(echo "$DATABASE_URL" | sed -E 's|.*/([^/?]+).*|\1|')

#  echo "PSQL path: $(which psql)"
 psql --version || echo "psql not found in PATH"

# # # Check if target database exists; create if it doesn't
 echo "🧾 Checking if database '$TARGET_DB' exists..."
 psql "$ADMIN_DB_URL" -tAc "SELECT 1 FROM pg_database WHERE datname = '$TARGET_DB'" | grep -q 1 || {
     echo "🆕 Database '$TARGET_DB' does not exist. Creating..."
     psql "$ADMIN_DB_URL" -c "CREATE DATABASE \"$TARGET_DB\""
 }

# Check if the environment is staging or production
if [ "$NODE_ENV" = "development" ]; then
    echo "🧪 Environment: DEVELOPMENT"
    if [ "$RESET_DB" = "1" ]; then
        echo "🔄 Resetting database..."
        npx prisma migrate reset --force --schema=./prisma/schema.prisma
    fi

    # Use migrate deploy for Docker to avoid interactive prompts
    echo "🔁 Running 'migrate deploy' for Docker environment"
    npx prisma migrate deploy --schema=./prisma/schema.prisma

    echo "🌱 Running Prisma seed..."
    npm run seed
else
    echo "🚀 Environment: PRODUCTION"
    echo "Running 'migrate deploy'"
    npx prisma migrate deploy --schema=./prisma/schema.prisma
fi

echo "✅ Migrations complete. Starting app..."

# # Check REDIS_URL and test Redis connection
# if [ -z "$REDIS_URL" ]; then
#     echo "❌ REDIS_URL is not set!"
#     exit 1
# else
#     echo "🔗 REDIS_URL: $REDIS_URL"
#     # Try to ping Redis
#     if command -v redis-cli > /dev/null; then
#         redis-cli -u "$REDIS_URL" ping | grep -q PONG
#         if [ $? -eq 0 ]; then
#             echo "✅ Redis connection successful."
#         else
#             echo "❌ Unable to communicate with Redis at $REDIS_URL"
#             exit 1
#         fi
#     else
#         echo "⚠️ redis-cli not found; skipping Redis connectivity check."
#     fi
# fi

# # Node.js connectivity check using ioredis
#     echo "🔍 Checking Redis connectivity with Node.js/ioredis..."
#     node -e "
#         const Redis = require('ioredis');
#         const redis = new Redis(process.env.REDIS_URL);
#         redis.ping()
#             .then(res => {
#                 if (res === 'PONG') {
#                     console.log('✅ Node.js/ioredis can connect to Redis.');
#                     process.exit(0);
#                 } else {
#                     console.error('❌ Node.js/ioredis ping failed:', res);
#                     process.exit(1);
#                 }
#             })
#             .catch(err => {
#                 console.error('❌ Node.js/ioredis connection error:', err.message);
#                 process.exit(1);
#             });
#     "
#     if [ $? -ne 0 ]; then
#         echo "❌ Node.js/ioredis cannot connect to Redis. Check REDIS_URL and network."
#         exit 1
#     fi

# Set RAILS_ENV to development if not already set
export NODE_ENV=${NODE_ENV:-development}
echo "NODE_ENV is set to $NODE_ENV"
if [ -n "$SOURCE_IP" ]; then
  export SOURCE_IP
  echo "SOURCE_IP is set to $SOURCE_IP"
else
  echo "SOURCE_IP is not set"
fi
export PORT=${PORT:-3000}
echo "PORT is set to $PORT"
export COOKIE_DOMAIN=${COOKIE_DOMAIN:-.dev1.ngnair.com}
echo "COOKIE_DOMAIN is set to $COOKIE_DOMAIN"

# Start your NestJS app (adjust as needed)
exec node dist/main.js