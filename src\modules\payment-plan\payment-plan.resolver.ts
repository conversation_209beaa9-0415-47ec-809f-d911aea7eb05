import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { PaymentPlanService } from './payment-plan.service';
import { PaymentPlanEntity } from './entities/payment-plan.entity';
import { CreatePaymentPlanDto } from './dto/create-payment-plan.dto';
import { UpdatePaymentPlanDto } from './dto/update-payment-plan.dto';
import { ProductEntity } from '../product/entities/product.entity';

@Resolver(() => PaymentPlanEntity)
export class PaymentPlanResolver {
  constructor(private readonly paymentPlanService: PaymentPlanService) {}

  @Public()
  @Query(() => [PaymentPlanEntity], { name: 'paymentPlans' })
  async findAll(
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.paymentPlanService.findAllByProduct(productId);
  }

  @Public()
  @Query(() => PaymentPlanEntity, { name: 'paymentPlan' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.paymentPlanService.findOne(id, productId);
  }

  @Mutation(() => PaymentPlanEntity)
  async createPaymentPlan(
    @Args('createPaymentPlanInput') createPaymentPlanDto: CreatePaymentPlanDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!createPaymentPlanDto.productId) {
      throw new BadRequestException('productId is required');
    }
    if (!createPaymentPlanDto.name) {
      throw new BadRequestException('name is required');
    }
    if (!createPaymentPlanDto.duration) {
      throw new BadRequestException('duration is required');
    }
    if (createPaymentPlanDto.price === undefined || createPaymentPlanDto.price === null) {
      throw new BadRequestException('price is required');
    }

    return this.paymentPlanService.create(createPaymentPlanDto);
  }

  @Mutation(() => PaymentPlanEntity)
  async updatePaymentPlan(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Args('updatePaymentPlanInput') updatePaymentPlanDto: UpdatePaymentPlanDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.paymentPlanService.update(id, productId, updatePaymentPlanDto);
  }

  @Mutation(() => Boolean)
  async removePaymentPlan(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    await this.paymentPlanService.remove(id, productId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => ProductEntity)
  async product(@Parent() paymentPlan: PaymentPlanEntity) {
    return paymentPlan.product;
  }
}
