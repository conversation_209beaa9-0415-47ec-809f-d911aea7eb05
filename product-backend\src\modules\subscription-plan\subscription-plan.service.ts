
import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateSubscriptionPlanDto } from './dto/create-subscription-plan.dto';
import { TransactionLogService } from '../transaction-log/transaction-log.service';

@Injectable()
export class SubscriptionPlanService {
	constructor(
		private readonly prisma: PrismaService,
		private readonly transactionLogService: TransactionLogService,
	) {}

		async create(dto: CreateSubscriptionPlanDto, merchantId: string) {
		// Validate product ownership
		const product = await this.prisma.product.findUnique({ where: { id: dto.productId } });
		if (!product) throw new NotFoundException('Product not found');
		if (product.merchantId !== merchantId) throw new ForbiddenException('You do not own this product');
			try {
				const created = await this.prisma.subscriptionPlan.create({ data: dto });
				// Audit log
				await this.transactionLogService.createLog({
					action: 'CREATE',
					entity: 'SubscriptionPlan',
					entityId: created.id,
					userId: merchantId,
					newValue: created,
				});
				return created;
			} catch (error) {
				throw new BadRequestException('Failed to create subscription plan');
			}
	}

	async findOne(id: string) {
		const plan = await this.prisma.subscriptionPlan.findUnique({ where: { id } });
		if (!plan) throw new NotFoundException('Subscription plan not found');
		return plan;
	}

	async findAll() {
		return this.prisma.subscriptionPlan.findMany();
	}

		async update(id: string, dto: Partial<CreateSubscriptionPlanDto>, merchantId: string) {
		// Validate plan and product ownership
		const plan = await this.prisma.subscriptionPlan.findUnique({
			where: { id },
			include: { product: true },
		});
		if (!plan) throw new NotFoundException('Subscription plan not found');
		if (plan.product.merchantId !== merchantId) throw new ForbiddenException('You do not own this product');
			try {
				const before = await this.prisma.subscriptionPlan.findUnique({ where: { id } });
				const updated = await this.prisma.subscriptionPlan.update({ where: { id }, data: dto });
				// Audit log
				await this.transactionLogService.createLog({
					action: 'UPDATE',
					entity: 'SubscriptionPlan',
					entityId: updated.id,
					userId: merchantId,
					oldValue: before,
					newValue: updated,
				});
				return updated;
			} catch (error) {
				throw new BadRequestException('Failed to update subscription plan');
			}
	}

		async remove(id: string, merchantId: string) {
			try {
				const before = await this.prisma.subscriptionPlan.findUnique({ where: { id } });
				const deleted = await this.prisma.subscriptionPlan.delete({ where: { id } });
				// Audit log
				await this.transactionLogService.createLog({
					action: 'DELETE',
					entity: 'SubscriptionPlan',
					entityId: deleted.id,
					userId: merchantId,
					oldValue: before,
				});
				return deleted;
			} catch (error) {
				throw new BadRequestException('Failed to delete subscription plan');
			}
	}
}
