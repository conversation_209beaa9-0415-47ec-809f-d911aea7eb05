import { InputType, Field } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, <PERSON>N<PERSON><PERSON>, Min, Max, IsString } from 'class-validator';

@InputType()
export class GetUsersDto {
  @ApiProperty({ 
    description: 'Page number for pagination',
    example: 1,
    required: false,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Field({ nullable: true })
  page?: number = 1;

  @ApiProperty({ 
    description: 'Number of items per page',
    example: 10,
    required: false,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Field({ nullable: true })
  limit?: number = 10;

  @ApiProperty({ 
    description: 'Filter by role',
    example: 'global_admin',
    required: false
  })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  role?: string;

  @ApiProperty({ 
    description: 'Filter by active status',
    example: true,
    required: false
  })
  @IsOptional()
  @Field({ nullable: true })
  active?: boolean;

  @ApiProperty({ 
    description: 'Search by email or name',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  search?: string;
}
