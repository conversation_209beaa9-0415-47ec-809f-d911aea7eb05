import { Resolver, Query, Args, ID, Context, Int } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { TransactionLogService } from './transaction-log.service';
import { TransactionLogEntity } from './entities/transaction-log.entity';
import { QueryTransactionLogDto } from './dto/query-transaction-log.dto';

@Resolver(() => TransactionLogEntity)
export class TransactionLogResolver {
  constructor(private readonly transactionLogService: TransactionLogService) {}

  @Public()
  @Query(() => [TransactionLogEntity], { name: 'transactionLogs' })
  async findAll(
    @Args('entity', { nullable: true }) entity?: string,
    @Args('entityId', { nullable: true }) entityId?: string,
    @Args('userId', { nullable: true }) userId?: string,
    @Args('action', { nullable: true }) action?: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 20 }) limit?: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset?: number,
    @Context() context?: any
  ) {
    const query: QueryTransactionLogDto = {
      entity,
      entityId,
      userId,
      action,
      limit,
      offset,
    };
    return this.transactionLogService.findAll(query);
  }

  @Public()
  @Query(() => TransactionLogEntity, { name: 'transactionLog' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Context() context?: any
  ) {
    if (!id) {
      throw new BadRequestException('id is required');
    }
    const log = await this.transactionLogService.findOne(id);
    if (!log) {
      throw new BadRequestException('Transaction log not found');
    }
    return log;
  }

  @Query(() => [TransactionLogEntity], { name: 'transactionLogsByMerchant' })
  async findByMerchant(
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Args('entity', { nullable: true }) entity?: string,
    @Args('entityId', { nullable: true }) entityId?: string,
    @Args('action', { nullable: true }) action?: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 20 }) limit?: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset?: number,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }

    const query: QueryTransactionLogDto = {
      entity,
      entityId,
      action,
      limit,
      offset,
    };
    return this.transactionLogService.findByMerchant(finalMerchantId, query);
  }
}
