import { IsString, IsOptional, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateCategoryDto {
  @Field()
  @ApiProperty({
    description: 'Category name',
    example: 'Electronics',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  name!: string;

  @Field()
  @ApiProperty({
    description: 'Merchant ID that owns this category',
    example: 'merchant_12345',
    format: 'uuid'
  })
  @IsString()
  merchantId!: string;

  @Field(() => [String], { nullable: true })
  @ApiProperty({
    description: 'Array of subcategory names to create under this category',
    example: ['Smartphones', 'Laptops', 'Headphones'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  subCategories?: string[]; // Array of subcategory names
}

export class GetCategoryDto {
  @ApiProperty({
    description: 'Merchant ID to filter categories',
    example: 'merchant_12345',
    format: 'uuid'
  })
  @IsString()
  merchantId!: string;
}

@InputType()
export class UpdateCategoryDto {
  @Field({ nullable: true })
  @ApiProperty({
    description: 'Updated category name',
    example: 'Consumer Electronics',
    minLength: 1,
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => [String], { nullable: true })
  @ApiProperty({
    description: 'Updated array of subcategory names',
    example: ['Mobile Devices', 'Computing', 'Audio Equipment'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  subCategories?: string[]; // Array of subcategory names for update
}