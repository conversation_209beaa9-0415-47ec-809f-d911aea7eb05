import { 
  Controller, 
  Get, 
  Post, 
  Put,
  Delete,
  Body, 
  Param,
  Query,
  UseGuards,
  Request,
  BadRequestException,
  NotFoundException
} from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags, ApiQuery, ApiResponse } from "@nestjs/swagger";
import { CustomValue } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { AuthenticatedRequest } from "../auth/src/types";
import { CreateCustomValueDto, UpdateCustomValueDto } from "./dto/custom-value.dto";
import { CustomValueService } from "./custom-value.service";

@ApiTags("Custom Values")
@Controller("custom-values")
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class CustomValueController {
  constructor(private readonly customValueService: CustomValueService) {}

  @Post()
  @ApiOperation({ summary: "Create a new custom value" })
  @ApiResponse({ status: 201, description: "Custom value created successfully" })
  @ApiResponse({ status: 409, description: "Custom value with this key already exists" })
  async create(
    @Body() createDto: CreateCustomValueDto,
    @Request() req: AuthenticatedRequest
  ): Promise<CustomValue> {
    return this.customValueService.create(createDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: "Get all custom values" })
  @ApiQuery({ name: "category", required: false })
  @ApiResponse({ status: 200, description: "List of custom values" })
  async findAll(@Query("category") category?: string): Promise<CustomValue[]> {
    return this.customValueService.findAll(category);
  }

  @Get(":key")
  @ApiOperation({ summary: "Get a custom value by key" })
  @ApiResponse({ status: 200, description: "Custom value found" })
  @ApiResponse({ status: 404, description: "Custom value not found" })
  async findOne(@Param("key") key: string): Promise<CustomValue> {
    return this.customValueService.findByKey(key);
  }

  @Put(":key")
  @ApiOperation({ summary: "Update a custom value" })
  @ApiResponse({ status: 200, description: "Custom value updated" })
  @ApiResponse({ status: 404, description: "Custom value not found" })
  async update(
    @Param("key") key: string,
    @Body() updateDto: UpdateCustomValueDto,
    @Request() req: AuthenticatedRequest
  ): Promise<CustomValue> {
    return this.customValueService.update(key, updateDto, req.user.id);
  }

  @Delete(":key")
  @ApiOperation({ summary: "Delete a custom value" })
  @ApiResponse({ status: 200, description: "Custom value deleted" })
  @ApiResponse({ status: 404, description: "Custom value not found" })
  @ApiResponse({ status: 400, description: "Cannot delete system value" })
  async remove(@Param("key") key: string): Promise<CustomValue> {
    return this.customValueService.delete(key);
  }

  // Helper endpoints for getting typed values
  @Get(":key/number")
  @ApiOperation({ summary: "Get a custom value as a number" })
  async getNumber(@Param("key") key: string): Promise<{ value: number }> {
    const value = await this.customValueService.getNumber(key);
    return { value };
  }

  @Get(":key/boolean")
  @ApiOperation({ summary: "Get a custom value as a boolean" })
  async getBoolean(@Param("key") key: string): Promise<{ value: boolean }> {
    const value = await this.customValueService.getBoolean(key);
    return { value };
  }

  @Get(":key/json")
  @ApiOperation({ summary: "Get a custom value as JSON" })
  async getJson(@Param("key") key: string): Promise<{ value: unknown }> {
    const value = await this.customValueService.getJson(key);
    return { value };
  }

  @Get(":key/string")
  @ApiOperation({ summary: "Get a custom value as a string" })
  async getString(@Param("key") key: string): Promise<{ value: string }> {
    const value = await this.customValueService.getString(key);
    return { value };
  }
}
