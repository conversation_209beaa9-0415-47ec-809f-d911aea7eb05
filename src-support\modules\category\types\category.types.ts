import { Prisma } from "@prisma/client";

export const categorySelect = {
  id: true,
  name: true,
  description: true,
  type: true,
  autoAssignTo: true,
  timeoutMinutes: true,
  escalateTo: true,
  createdAt: true,
  updatedAt: true
} as const;

export type CategorySelect = typeof categorySelect;
export type CategoryPayload = Prisma.CategoryGetPayload<{
  select: CategorySelect;
}>;

export type CreateCategoryParams = Pick<
  Prisma.CategoryCreateInput, 
  "name" | 
  "description" | 
  "type" | 
  "autoAssignTo" | 
  "timeoutMinutes" | 
  "escalateTo"
>;
