# GraphQL Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing the missing GraphQL resolvers in the product-backend system.

## Current Implementation Status

### ✅ Completed
- **Product Module**: Full CRUD operations with GraphQL resolver

### ❌ Missing Implementations
- Category Module
- SubCategory Module  
- Variant Module
- Discount Module
- Merchant Module
- Subscription Plan Module
- Payment Plan Module
- Transaction Log Module

## Implementation Steps

### 1. Category Module Implementation

#### Step 1.1: Create Category Resolver
Create `product-backend/src/modules/category/category.resolver.ts`:

```typescript
import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { CategoryService } from './category.service';
import { CategoryEntity } from './entities/category.entity';
import { CreateCategoryDto } from './dto/category.dto';
import { UpdateCategoryDto } from './dto/category.dto';

@Resolver(() => CategoryEntity)
export class CategoryResolver {
  constructor(private readonly categoryService: CategoryService) {}

  @Public()
  @Query(() => [CategoryEntity], { name: 'categories' })
  async findAll(@Args('merchantId') merchantId: string) {
    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.findAllByMerchant(merchantId);
  }

  @Public()
  @Query(() => CategoryEntity, { name: 'category' })
  async findOne(@Args('id', { type: () => ID }) id: string) {
    return this.categoryService.findOne(id);
  }

  @Mutation(() => CategoryEntity)
  async createCategory(
    @Args('input') input: CreateCategoryDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    const merchantId = context.req.user.merchantId || input.merchantId;
    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.create({ ...input, merchantId });
  }

  @Mutation(() => CategoryEntity)
  async updateCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('input') input: UpdateCategoryDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    const merchantId = context.req.user.merchantId;
    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.update(id, merchantId, input);
  }

  @Mutation(() => Boolean)
  async deleteCategory(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    const merchantId = context.req.user.merchantId;
    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    await this.categoryService.remove(id, merchantId);
    return true;
  }
}
```

#### Step 1.2: Update Category Module
Update `product-backend/src/modules/category/category.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { CategoryService } from './category.service';
import { CategoryController } from './category.controller';
import { CategoryResolver } from './category.resolver'; // Add this
import { PrismaModule } from '../../prisma/prisma.module';
import { TransactionLogModule } from '../transaction-log/transaction-log.module';

@Module({
  imports: [PrismaModule, TransactionLogModule],
  controllers: [CategoryController],
  providers: [CategoryService, CategoryResolver], // Add CategoryResolver
  exports: [CategoryService],
})
export class CategoryModule {}
```

### 2. SubCategory Module Implementation

#### Step 2.1: Create SubCategory Resolver
Create `product-backend/src/modules/subcategory/subcategory.resolver.ts`:

```typescript
import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { SubCategoryService } from './subcategory.service';
import { SubCategoryEntity } from './entities/subcategory.entity';
import { CreateSubCategoryDto, UpdateSubCategoryDto } from './dto/subcategory.dto';

@Resolver(() => SubCategoryEntity)
export class SubCategoryResolver {
  constructor(private readonly subCategoryService: SubCategoryService) {}

  @Public()
  @Query(() => [SubCategoryEntity], { name: 'subCategories' })
  async findAll(@Args('categoryId') categoryId: string) {
    if (!categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.subCategoryService.findAllByCategory(categoryId);
  }

  @Public()
  @Query(() => SubCategoryEntity, { name: 'subCategory' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string
  ) {
    return this.subCategoryService.findOne(id, categoryId);
  }

  @Mutation(() => SubCategoryEntity)
  async createSubCategory(
    @Args('input') input: CreateSubCategoryDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    if (!input.categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.subCategoryService.create(input);
  }

  @Mutation(() => SubCategoryEntity)
  async updateSubCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string,
    @Args('input') input: UpdateSubCategoryDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    return this.subCategoryService.update(id, categoryId, input);
  }

  @Mutation(() => Boolean)
  async deleteSubCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    await this.subCategoryService.remove(id, categoryId);
    return true;
  }
}
```

### 3. Variant Module Implementation

#### Step 3.1: Create Variant Resolver
Create `product-backend/src/variant/variant.resolver.ts`:

```typescript
import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../modules/auth/src/auth.guard';
import { VariantService } from './variant.service';
import { VariantEntity } from './entities/variant.entity';
import { CreateVariantDto, UpdateVariantDto } from './dto/variant.dto';

@Resolver(() => VariantEntity)
export class VariantResolver {
  constructor(private readonly variantService: VariantService) {}

  @Public()
  @Query(() => [VariantEntity], { name: 'variants' })
  async findAll(@Args('productId') productId: string) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.variantService.findAllByProduct(productId);
  }

  @Public()
  @Query(() => VariantEntity, { name: 'variant' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string
  ) {
    return this.variantService.findOne(id, productId);
  }

  @Mutation(() => VariantEntity)
  async createVariant(
    @Args('input') input: CreateVariantDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    if (!input.productId) {
      throw new BadRequestException('productId is required');
    }
    return this.variantService.create(input);
  }

  @Mutation(() => VariantEntity)
  async updateVariant(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Args('input') input: UpdateVariantDto,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    return this.variantService.update(id, productId, input);
  }

  @Mutation(() => Boolean)
  async deleteVariant(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context: any
  ) {
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }
    await this.variantService.remove(id, productId);
    return true;
  }
}
```

## Implementation Checklist

### For Each Module:
- [ ] Create `[module].resolver.ts` file
- [ ] Add resolver to module providers
- [ ] Implement Query resolvers
- [ ] Implement Mutation resolvers  
- [ ] Add proper authentication guards
- [ ] Add input validation
- [ ] Test with GraphQL playground

### Priority Order:
1. **Category** (High Priority - Core functionality)
2. **SubCategory** (High Priority - Core functionality)
3. **Variant** (High Priority - Product variations)
4. **Merchant** (High Priority - Multi-tenancy)
5. **Discount** (Medium Priority - Business features)
6. **Subscription Plan** (Medium Priority - Recurring billing)
7. **Payment Plan** (Medium Priority - Installments)
8. **Transaction Log** (Low Priority - Audit trails)

## Testing Each Implementation

### 1. Test Queries
```graphql
query TestCategories {
  categories(merchantId: "test_merchant") {
    id
    name
  }
}
```

### 2. Test Mutations
```graphql
mutation TestCreateCategory {
  createCategory(input: {
    name: "Test Category"
    merchantId: "test_merchant"
  }) {
    id
    name
  }
}
```

### 3. Verify Schema Generation
After implementing each resolver:
1. Start the server
2. Check that `src/schema.gql` is updated
3. Verify new types appear in GraphQL playground
4. Test introspection queries

## Common Patterns

### Authentication Pattern
```typescript
if (!context?.req?.user) {
  throw new UnauthorizedException('Authentication required');
}
const merchantId = context.req.user.merchantId;
```

### Validation Pattern
```typescript
if (!merchantId) {
  throw new BadRequestException('merchantId is required');
}
```

### Public Query Pattern
```typescript
@Public()
@Query(() => [EntityType], { name: 'entities' })
async findAll(@Args('merchantId') merchantId: string) {
  // Implementation
}
```
