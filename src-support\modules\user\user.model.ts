import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class User {
  @Field(() => ID)
  id: string;

  @Field()
  email: string;

  @Field()
  firstName: string;

  @Field()
  lastName: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  country?: string;

  @Field()
  verifiedEmail: boolean;

  @Field()
  verifiedPhone: boolean;

  @Field()
  role: string;

  @Field()
  createdAt: Date;

  @Field(() => [String])
  partnerId: string[];

  @Field()
  mfaEnabled: boolean;

  @Field()
  active: boolean;

  @Field({ nullable: true })
  accountId?: string;
}

@ObjectType()
export class UserListResponse {
  @Field(() => [User])
  items: User[];

  @Field()
  total: number;

  @Field()
  page: number;

  @Field()
  limit: number;

  @Field()
  pages: number;
}
