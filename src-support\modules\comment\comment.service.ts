import { Injectable, NotFoundException } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import { CommentPayload, commentSelect } from "./types/comment.types";

@Injectable()
export class CommentService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: Prisma.CommentCreateInput): Promise<CommentPayload> {
    return this.prisma.comment.create({
      data,
      select: commentSelect
    });
  }

  async findByTicketId(ticketId: string): Promise<CommentPayload[]> {
    return this.prisma.comment.findMany({
      where: {
        ticketId
      },
      select: commentSelect,
      orderBy: {
        createdAt: "desc"
      }
    });
  }

  async delete(where: Prisma.CommentWhereUniqueInput): Promise<CommentPayload> {
    const comment = await this.prisma.comment.findUnique({
      where,
      select: commentSelect
    });

    if (!comment) {
      throw new NotFoundException(`Comment with ID ${where.id} not found`);
    }

    return this.prisma.comment.delete({
      where,
      select: commentSelect
    });
  }
}
