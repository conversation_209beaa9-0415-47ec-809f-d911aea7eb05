import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { Public } from './modules/auth/src/auth.guard';

@ApiTags('General')
@Controller()
export class AppController {
	constructor(private readonly appService: AppService) {}

	@Public()
	@Get()
	@ApiOperation({ summary: 'Get welcome message' })
	@ApiResponse({ status: 200, description: 'Welcome message returned successfully' })
	getHello(): string {
		return this.appService.getHello();
	}

	@Public()
	@Get('health')
	@ApiOperation({ summary: 'Health check endpoint' })
	@ApiResponse({ status: 200, description: 'Service is healthy', schema: {
		type: 'object',
		properties: {
			status: { type: 'string', example: 'ok' },
			timestamp: { type: 'string', example: '2025-09-10T08:00:00.000Z' }
		}
	}})
	getHealth(): object {
		return { status: 'ok', timestamp: new Date().toISOString() };
	}
}
