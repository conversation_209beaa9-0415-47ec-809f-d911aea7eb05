import { ApiProperty } from '@nestjs/swagger';
import { InputType, ObjectType, Field, ID, Int } from '@nestjs/graphql';

@ObjectType('FileResponse')
@InputType('FileResponseInput')
export class FileResponseDto {
  @Field(() => ID)
  @ApiProperty()
  id: string;

  @Field()
  @ApiProperty()
  filename: string;

  @Field(() => Int)
  @ApiProperty()
  fileSize: number;

  @Field()
  @ApiProperty()
  fileUrl: string;

  @Field()
  @ApiProperty()
  mimeType: string;

  @Field()
  @ApiProperty()
  uploadedBy: string;

  @Field({ nullable: true })
  @ApiProperty({ required: false, nullable: true })
  ticketId: string | null;

  @Field({ nullable: true })
  @ApiProperty({ required: false, nullable: true })
  commentId: string | null;

  @Field()
  @ApiProperty()
  uploadedAt: Date;
}
