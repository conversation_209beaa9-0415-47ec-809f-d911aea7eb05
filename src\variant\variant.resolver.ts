import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../modules/auth/src/auth.guard';
import { VariantService } from './variant.service';
import { VariantEntity } from './entities/variant.entity';
import { CreateVariantDto, UpdateVariantDto } from './dto/variant.dto';
import { ProductEntity } from '../modules/product/entities/product.entity';

@Resolver(() => VariantEntity)
export class VariantResolver {
  constructor(private readonly variantService: VariantService) {}

  @Public()
  @Query(() => [VariantEntity], { name: 'variants' })
  async findAll(
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.variantService.findAllByProduct(productId);
  }

  @Public()
  @Query(() => VariantEntity, { name: 'variant' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.variantService.findOne(id, productId);
  }

  @Mutation(() => VariantEntity)
  async createVariant(
    @Args('createVariantInput') createVariantDto: CreateVariantDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!createVariantDto.productId) {
      throw new BadRequestException('productId is required');
    }
    if (!createVariantDto.name) {
      throw new BadRequestException('name is required');
    }
    if (!createVariantDto.sku) {
      throw new BadRequestException('sku is required');
    }

    return this.variantService.create(createVariantDto);
  }

  @Mutation(() => VariantEntity)
  async updateVariant(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Args('updateVariantInput') updateVariantDto: UpdateVariantDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.variantService.update(id, productId, updateVariantDto);
  }

  @Mutation(() => Boolean)
  async removeVariant(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    await this.variantService.remove(id, productId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => ProductEntity)
  async product(@Parent() variant: VariantEntity) {
    return variant.product;
  }
}
