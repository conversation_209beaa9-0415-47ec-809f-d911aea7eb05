import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards, NotFoundException, BadRequestException } from '@nestjs/common';
import { TicketService } from './ticket.service';
import { TicketModel } from './ticket.model';
import { CreateTicketInput } from './ticket.input';
import { UpdateTicketDto } from './dto/update-ticket.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';

@Resolver(() => TicketModel)
export class TicketResolver {
  constructor(private readonly ticketService: TicketService) {}

  @Query(() => [TicketModel], { name: 'tickets' })
  async tickets() {
    // Fetch all tickets
    return this.ticketService.findMany();
  }

  @Mutation(() => TicketModel)
  @UseGuards(GraphQLAuthGuard)
  async createTicket(
    @Args('data') data: CreateTicketInput,
    @Context() context: any
  ) {
    const user = context.req.user;
    console.log('createTicket input data:', data, JSON.stringify(data));
    return this.ticketService.create({
      ...data,
      createdBy: user.id,
      lastUpdatedBy: user.id
    });
  }

  @Query(() => TicketModel, { name: 'ticket' })
  @UseGuards(GraphQLAuthGuard)
  async getTicket(@Args('id') id: string) {
    const ticket = await this.ticketService.findUnique({ id });
    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return ticket;
  }

  @Mutation(() => TicketModel)
  @UseGuards(GraphQLAuthGuard)
  async updateTicket(
    @Args('id') id: string,
    @Args('data') data: UpdateTicketDto,
    @Context() context: any
  ) {
    const user = context.req.user;

    // Check if ticket exists
    const existingTicket = await this.ticketService.findUnique({ id });
    if (!existingTicket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    try {
      const result = await this.ticketService.update(
        { id },
        { ...data, lastUpdatedBy: user.id }
      );

      if (!result) {
        throw new NotFoundException(`Ticket with ID ${id} not found`);
      }

      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while updating the ticket: ${errorMessage}`
      );
    }
  }

  @Mutation(() => TicketModel)
  @UseGuards(GraphQLAuthGuard)
  async deleteTicket(@Args('id') id: string) {
    // Check if ticket exists
    const existingTicket = await this.ticketService.findUnique({ id });
    if (!existingTicket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    try {
      const result = await this.ticketService.delete({ id });
      if (!result) {
        throw new NotFoundException(`Ticket with ID ${id} not found`);
      }
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while deleting the ticket: ${errorMessage}`
      );
    }
  }
}
