# 🚀 Complete GraphQL API Documentation

## 🔗 **GraphQL Playground Access**
- **URL**: `http://localhost:3070/graphql`
- **Authentication**: Required via `access_token` cookie
- **Headers**: Set cookies in HTTP Headers tab

## 🍪 **Authentication Setup**

### Step 1: Set Authentication Cookie
In GraphQL Playground, go to **HTTP HEADERS** tab and add:
```json
{
  "Cookie": "access_token=YOUR_ACCESS_TOKEN_HERE"
}
```

### Step 2: Test Token (Provided)
```
aXMxa3llRk50dFEvVGxkV2sxMDc0SmNCQmE1bkFiRGpycWE2WHBQT0RSeEhXOE5oZEhSZFlsZzNyQkxXR0srNHE3c2JjUWNiSHN1MGdTVExjQlRmUWVrV3plMTlzUnpEbFZjZVJKZFdhLzljRHlNdlBtNjRxSXdFVDdnR3hFZUdjeWx2U1VmeEx4dkpYblVhS1d3aEVnZkx5S1VMdGNNZFY5TTZER2RFUGZkeE9lMy9sWGpXOGtMa1dWSTkybWs4Q2xpb3ZsYWpQSmVPRS9paDFzcCtEVVZJUEdpbHlVL0lmTGhka1Q0cG5tRVJpcE85aTYxZVo5Q3poNThQMUxHbFRnVlEwenFpK056SysvQ2VRM2puY3ZsM2JtRHByWVQ5ZzFkSGdWeFNDTkZmQi9rMzExcGhYclQzKzNFdUQxUG10MVkvMHhNQlBpazkvRUVMQ1hjc1IxQW9yTDZybDk3c2VRUVd2L3kzVnRrSjVqeWtpeTYyaFM0L0F2bytBeFE1SGF5MC9rVFYxeEZzbEljeStKWUFnNGJ2ZmtXblB5MHFZSW9XanEzWWpjYXlvUVNQWS95TFJKS2NSM0NVMU04SjZTeTJ2WWZYUitFODBwMXJFWFU0Y3d6QjlZRzIvek1WWTNSaW9RQTk5SkdNN0ZvbUF5amthTzhqZGZUcGZVUTNoYmJuaHVmallZQk1SSUJwRDJZbE8wdXNSV1R2UksyWmVNTTNOSVV2bm5BVCtYTHJmVzB4blhqMGZ4QVZzeE8xZmhyRWErdnFzU3luWjVobGxpMnlUa1pwb0RhS3hkUVZhTzNBb3kzaVBsQkRtWkFaMGtvTmsvOUI2cGZEZVlaZEE5SHYzcXZZRkxhQ3ZrNFFJSGJPNWlNTmdvRzMraGVTZWZNSitaemYyWVJKbGI4QkVkVmVBaWswa1cranhPaVdSVUNHdVNWejlla1lmdnZnZENpQnJrRW9UL3ZFUktWUXRFdS9ldDJlMHgxdFdaQXNDR2J1MUppUTViL3ZTYkhaMWhXVWwxM2c5Y3dnczdwQTd4d3RWVEtXSkJGeDZKTUlvSnJLUmRwWGc4a251MElCbFZuaGNlOHhOWlNRWU4wTjlxY3VHaXBxSjdSNkZ2bXc2OWpoWGxpTW0xeTl6MmNqZGM4b055MGo0bGxPdnU2MVJoQlgrcHhkeGoxR1RPUlJodFFNZnZRd1pWb0ZvcWpScDJrUnV6YUI1Uk1maGVNZkdNOWZLNHRkeHBNbU1TcWo4RTZFVWs0am9KQUw2SUlwODVnRjJNbFgyMUNWL21BeUk1dkVXM24wdnRFWU1qOG92VVBNOW9sNVd4bmxqRkJWeXRGTlZQd1VuSTlHaktDZGhxTGRUR3paUzRiN1Qwbi9aRmZQOGFYUHBJZk11MjQ0dW9NNnUxUTlMWDRNbTllWGNKd3praS9GcW9CbEdQejFKb3lnT2hsanZBTXpEK2tabEhHYTFuRUpTOW1xQWV6VXdrL1YwUT09LS1rVkJUSkNUZlVxcnBqbEwxLS05bGQzVVZTeWVGK25PeElVZHI1eDdRPT0%3D
```

---

## 📋 **Available GraphQL Operations**

### 🛍️ **1. PRODUCTS** ✅ (Fully Implemented)

#### **Queries**

##### Get All Products
```graphql
query GetProducts($merchantId: String) {
  products(merchantId: $merchantId) {
    id
    name
    price
    sku
    count
    categoryId
    subCategoryId
    merchantId
    description
    status
    saleType
    createdAt
    updatedAt
    deletedAt
  }
}
```

**Variables:**
```json
{
  "merchantId": "test-merchant"
}
```

##### Get Single Product
```graphql
query GetProduct($id: ID!, $merchantId: String) {
  product(id: $id, merchantId: $merchantId) {
    id
    name
    price
    sku
    description
    status
    saleType
    createdAt
    updatedAt
  }
}
```

**Variables:**
```json
{
  "id": "product_id_here",
  "merchantId": "test-merchant"
}
```

#### **Mutations**

##### Create Product
```graphql
mutation CreateProduct($createProductInput: CreateProductDto!) {
  createProduct(createProductInput: $createProductInput) {
    id
    name
    price
    sku
    categoryId
    merchantId
    status
    saleType
    createdAt
    updatedAt
  }
}
```

**Variables:**
```json
{
  "createProductInput": {
    "name": "Premium Wireless Headphones",
    "price": 2999,
    "sku": "PWH-001-BLK",
    "categoryId": "category_id_here",
    "description": "High-quality wireless headphones with noise cancellation",
    "count": 100,
    "status": "ACTIVE",
    "saleType": "CASH"
  }
}
```

##### Update Product
```graphql
mutation UpdateProduct($id: ID!, $updateProductInput: UpdateProductDto!) {
  updateProduct(id: $id, updateProductInput: $updateProductInput) {
    id
    name
    price
    sku
    description
  }
}
```

##### Delete Product
```graphql
mutation RemoveProduct($id: ID!) {
  removeProduct(id: $id)
}
```

---

### 🏷️ **2. CATEGORIES** ✅ (Fully Implemented)

#### **Queries**

##### Get All Categories
```graphql
query GetCategories($merchantId: String) {
  categories(merchantId: $merchantId) {
    id
    name
    merchantId
    createdAt
    deletedAt
  }
}
```

##### Get Single Category
```graphql
query GetCategory($id: ID!, $merchantId: String) {
  category(id: $id, merchantId: $merchantId) {
    id
    name
    merchantId
  }
}
```

#### **Mutations**

##### Create Category
```graphql
mutation CreateCategory($createCategoryInput: CreateCategoryDto!) {
  createCategory(createCategoryInput: $createCategoryInput) {
    id
    name
    merchantId
  }
}
```

**Variables:**
```json
{
  "createCategoryInput": {
    "name": "Electronics",
    "merchantId": "test-merchant"
  }
}
```

##### Update Category
```graphql
mutation UpdateCategory($id: ID!, $updateCategoryInput: UpdateCategoryDto!) {
  updateCategory(id: $id, updateCategoryInput: $updateCategoryInput) {
    id
    name
    merchantId
  }
}
```

##### Delete Category
```graphql
mutation RemoveCategory($id: ID!) {
  removeCategory(id: $id)
}
```

---

### 🏪 **3. MERCHANTS** ✅ (Fully Implemented)

#### **Queries**

##### Get All Merchants
```graphql
query GetMerchants {
  merchants {
    id
    name
    createdAt
    deletedAt
  }
}
```

##### Get Single Merchant
```graphql
query GetMerchant($id: ID!) {
  merchant(id: $id) {
    id
    name
    createdAt
  }
}
```

#### **Mutations**

##### Create Merchant
```graphql
mutation CreateMerchant($createMerchantInput: CreateMerchantDto!) {
  createMerchant(createMerchantInput: $createMerchantInput) {
    id
    name
  }
}
```

**Variables:**
```json
{
  "createMerchantInput": {
    "id": "new-merchant-id",
    "name": "New Merchant Store"
  }
}
```

##### Update Merchant
```graphql
mutation UpdateMerchant($id: ID!, $updateMerchantInput: CreateMerchantDto!) {
  updateMerchant(id: $id, updateMerchantInput: $updateMerchantInput) {
    id
    name
  }
}
```

##### Delete Merchant
```graphql
mutation RemoveMerchant($id: ID!) {
  removeMerchant(id: $id)
}
```

---

### 💰 **4. DISCOUNTS** ✅ (Fully Implemented)

#### **Queries**

##### Get All Discounts
```graphql
query GetDiscounts($merchantId: String) {
  discounts(merchantId: $merchantId) {
    id
    name
    description
    type
    discount
    maxDiscount
    validFrom
    validTo
    maxClaims
    claims
    status
    scope
    createdAt
    merchantId
  }
}
```

##### Get Single Discount
```graphql
query GetDiscount($id: ID!, $merchantId: String) {
  discount(id: $id, merchantId: $merchantId) {
    id
    name
    type
    discount
    status
  }
}
```

#### **Mutations**

##### Create Discount
```graphql
mutation CreateDiscount($createDiscountInput: CreateDiscountDto!) {
  createDiscount(createDiscountInput: $createDiscountInput) {
    id
    name
    type
    discount
    status
  }
}
```

**Variables:**
```json
{
  "createDiscountInput": {
    "name": "Summer Sale 2024",
    "description": "Get 20% off on all electronics",
    "type": "PERCENTAGE",
    "discount": 20,
    "maxDiscount": 5000,
    "status": "ACTIVE",
    "scope": "product",
    "merchantId": "test-merchant"
  }
}
```

---

### 🔄 **5. SUBSCRIPTION PLANS** ✅ (Fully Implemented)

#### **Queries**

##### Get Subscription Plans
```graphql
query GetSubscriptionPlans($productId: String!) {
  subscriptionPlans(productId: $productId) {
    id
    name
    description
    price
    recurringMode
    createdAt
  }
}
```

#### **Mutations**

##### Create Subscription Plan
```graphql
mutation CreateSubscriptionPlan($createSubscriptionPlanInput: CreateSubscriptionPlanDto!) {
  createSubscriptionPlan(createSubscriptionPlanInput: $createSubscriptionPlanInput) {
    id
    name
    price
    recurringMode
  }
}
```

---

### 💳 **6. PAYMENT PLANS** ✅ (Fully Implemented)

#### **Queries**

##### Get Payment Plans
```graphql
query GetPaymentPlans($productId: String!) {
  paymentPlans(productId: $productId) {
    id
    name
    description
    totalAmount
    installments
    frequency
    createdAt
  }
}
```

#### **Mutations**

##### Create Payment Plan
```graphql
mutation CreatePaymentPlan($createPaymentPlanInput: CreatePaymentPlanDto!) {
  createPaymentPlan(createPaymentPlanInput: $createPaymentPlanInput) {
    id
    name
    totalAmount
    installments
    frequency
  }
}
```

---

## 🔧 **GraphQL Schema Types**

### **Enums**
- `ProductStatus`: ACTIVE, INACTIVE, PENDING, DISCONTINUED
- `SaleType`: CASH, SUBSCRIPTION, INSTALLMENT
- `DiscountType`: FLAT, PERCENTAGE
- `DiscountStatus`: ACTIVE, INACTIVE, EXPIRED, SCHEDULED
- `PaymentPlanFrequency`: WEEKLY, MONTHLY, YEARLY

### **Input Types**
- `CreateProductDto`
- `UpdateProductDto`
- `CreateCategoryDto`
- `UpdateCategoryDto`
- `CreateMerchantDto`
- `CreateDiscountDto`
- `UpdateDiscountDto`
- `CreateSubscriptionPlanDto`
- `CreatePaymentPlanDto`

---

## ⚠️ **Important Notes**

1. **Authentication Required**: All mutations require authentication via access_token cookie
2. **Merchant Scoping**: Most operations are scoped to the authenticated user's merchant
3. **GraphQL Errors**: Returns HTTP 200 with errors in response body (standard GraphQL behavior)
4. **Field Resolvers**: Some nested fields may return empty arrays or null (marked as TODO in resolvers)

---

## 🧪 **Testing Workflow**

1. **Set Authentication**: Add access_token cookie to HTTP Headers
2. **Test Queries**: Start with simple queries like `merchants` or `categories`
3. **Test Mutations**: Create entities using the provided examples
4. **Verify Results**: Query the created entities to confirm success

---

## 📚 **Additional Resources**

- **Swagger API**: `http://localhost:3070/api`
- **Health Check**: `http://localhost:3070/api/v1/health`
- **GraphQL Schema**: Auto-generated at `product-backend/src/schema.gql`
