import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { CategoryService } from './category.service';
import { CategoryEntity } from './entities/category.entity';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { SubCategoryEntity } from '../subcategory/entities/subcategory.entity';
import { ProductEntity } from '../product/entities/product.entity';
import { MerchantEntity } from '../merchant/entities/merchant.entity';

@Resolver(() => CategoryEntity)
export class CategoryResolver {
  constructor(private readonly categoryService: CategoryService) {}

  @Public()
  @Query(() => [CategoryEntity], { name: 'categories' })
  async findAll(
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.findAllByMerchant(finalMerchantId);
  }

  @Public()
  @Query(() => CategoryEntity, { name: 'category' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.findOne(id);
  }

  @Mutation(() => CategoryEntity)
  async createCategory(
    @Args('createCategoryInput') createCategoryDto: CreateCategoryDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId || createCategoryDto.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    if (!createCategoryDto.name) {
      throw new BadRequestException('name is required');
    }

    // Ensure the category is created for the authenticated user's merchant
    const categoryData = { ...createCategoryDto, merchantId };
    return this.categoryService.create(categoryData);
  }

  @Mutation(() => CategoryEntity)
  async updateCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateCategoryInput') updateCategoryDto: UpdateCategoryDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.categoryService.update(id, merchantId, updateCategoryDto);
  }

  @Mutation(() => Boolean)
  async removeCategory(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    await this.categoryService.remove(id, merchantId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => [SubCategoryEntity])
  async subCategories(@Parent() category: CategoryEntity) {
    return category.subCategories || [];
  }

  @ResolveField(() => [ProductEntity])
  async products(@Parent() category: CategoryEntity) {
    return category.products || [];
  }

  @ResolveField(() => MerchantEntity)
  async merchant(@Parent() category: CategoryEntity) {
    return category.merchant;
  }
}
