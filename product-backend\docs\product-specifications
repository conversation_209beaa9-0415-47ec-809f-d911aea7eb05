Product Microservice – Specification 

# Product Microservice – Specification 
 
## Overview 
The **Product** microservice provides merchant-scoped catalog management for products, categories, variants, discounts, subscriptions, and payment plans. It integrates with other microservices in the payments platform. 
 
--- 
 
## Components 
 
### Back-End 
- **NestJS** application with Prisma ORM. 
- Database: **Postgres** with schema reflecting products, categories, variants, discounts, subscriptions, and plans. 
- Exposes **GraphQL** (frontend + admin) and **REST APIs** (service-to-service). 
 
### Front-End (Merchant & Customer) 
- GraphQL-driven queries for catalog browsing and product purchasing. 
- Merchant portal for creating and managing products, categories, discounts, and plans. 
 
### Admin Front-End (Internal) 
- GraphQL namespace `admin`. 
- Manage boundaries, alert rules, templates, feature toggles, and audits. 
- **Note:** Admin-specific entities (e.g., `MessageTemplate`, `AlertRule`, `BoundaryPolicy`, `FeatureToggle`, `AdminAuditLog`) are **not part of the initial build** but are recorded here for knowledge and future roadmap planning. 
 
--- 
 
## Domain Entities 
- **Merchant**: owns products, categories, and discounts. 
- **Category / SubCategory**: organizes catalog. 
- **Product**: core item, tied to merchant, with variants. 
- **ProductVariant**: supports SKUs, attributes, stock. 
- **Discount**: flat or percent, with caps, applied to product or cart. 
- **Subscription**: recurring monthly/annual plans. 
- **PaymentPlan**: finite installment schedules (e.g., $100/mo until paid). 
 
### Admin-Specific (Not in Initial Build) 
- **MessageTemplate**: customizable admin-level communication templates. 
- **AlertRule**: triggers for stock, discount expiry, or boundary violations. 
- **BoundaryPolicy**: constraints (e.g., max discount %). 
- **FeatureToggle**: enable/disable features per merchant. 
- **AdminAuditLog**: immutable admin action records. 
 
--- 
 
## Key Features 
- **Multi-tenancy**: merchant-isolated data and permissions. 
- **Discount Engine**: flexible rules, caps, and scopes. 
- **Plans**: mutually exclusive subscription vs payment plan per product. 
- **GraphQL**: rich queries for products, categories, discounts, plans. 
- **REST**: stable interservice contracts. 
- **Search & Filtering**: pagination, filtering, and indexing for products. 
- **Admin Customization (future)**: templates, alerts, policies, toggles, audits. 
 
--- 
 
## API Endpoints 
 
### GraphQL 
- `query { products, categories, variants, discounts, subscriptions, paymentPlans }` 
- `mutation { createProduct, updateProduct, deleteProduct, ... }` 
- `admin { createTemplate, setBoundary, publishAlert, toggleFeature, ... }` *(future)* 
 
### REST (service-to-service) 
- `GET /products` 
- `POST /products` 
- `GET /products/:id` 
- `PATCH /products/:id` 
- `DELETE /products/:id` 
- `GET /discounts` 
- `POST /discounts` 
- `GET /subscriptions` 
- `POST /subscriptions` 
- `GET /payment-plans` 
- `POST /payment-plans` 
 
--- 
 
## Security & Roles 
- Roles: `customer`, `merchant_user`, `merchant_admin`, `support_agent`, `internal_admin`. 
- RBAC on both GraphQL namespaces. 
- Tenant isolation via merchantId scoping. 
 
--- 
 
## Integration Points 
- **Finance**: consumes plans/discount data for payment execution. 
- **Accounts/Auth**: enforces roles and permissions. 
- **Support**: references product/discount context in tickets. 
 
--- 
 
## Admin-Specific Workflows (Future) 
- **Template Management**: create → draft → preview → publish. 
- **Alert Rules**: define triggers, test, and enable. 
- **Boundary Enforcement**: reject inputs that exceed policy. 
- **Feature Flags**: rollout to merchants/environments selectively. 
- **Auditing**: immutable change logs for compliance. 
 
--- 
 
## Phased Roadmap 
1. Core schema and Prisma setup. 
2. Merchant product and category CRUD. 
3. Discounts and variant modeling. 
4. Subscription and payment plan support. 
5. GraphQL & REST endpoints. 
6. Observability, monitoring, and auditing. 
7. Admin customization (templates, alerts, boundaries) — **future phase**. 
 
--- 
 
## Glossary 
- **ProductVariant**: specific SKU attributes. 
- **Subscription**: recurring billing. 
- **PaymentPlan**: fixed installment payoff. 
- **BoundaryPolicy**: enforced merchant-specific limits (future). 
- **FeatureToggle**: switch features on/off (future). 
- **MessageTemplate**: reusable comms config (future). 
- **AdminAuditLog**: trace of admin activity (future). 
 
 
 
---------------------------- 
Admin Entities (Deferred) – Markdown Addendum 

## Scope Note (Admin) 
The following **Admin-only** entities and workflows are **deferred** and **not part of v1** implementation. Keep them in the knowledge base for Phase 6 planning. 
 
### Deferred Entities 
- **MessageTemplate**: in-app/email/SMS templates; versioned; preview & rollback. 
- **AlertRule**: conditions and notifications for expirations/policy violations. 
- **BoundaryPolicy**: max discount %, plan term caps, late fee caps; enforced server-side. 
- **FeatureToggle**: merchant-scoped feature flags with targeting and rollout. 
- **AdminAuditLog**: immutable record of admin mutations. 
 
### Deferred Workflows 
- Assume Merchant Context (admin switch with UI banner). 
- Preview/Simulation tools (templates, discount outcomes, plan schedules). 
- Versioned publish + rollback; scheduled publishes. 
- Separate `admin` GraphQL namespace; strict RBAC. 
 
### Phase Target 
- **Phase 6 – Admin Features** (post v1 stabilization) 
 