import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateMerchantDto {
  @Field()
  @ApiProperty({
    description: 'Unique merchant ID',
    example: 'test-merchant'
  })
  @IsString()
  id!: string;

  @Field()
  @ApiProperty({
    description: 'Merchant name',
    example: 'Test Merchant Store'
  })
  @IsString()
  name!: string;
}

@InputType()
export class UpdateMerchantDto {
  @Field({ nullable: true })
  @ApiProperty({
    description: 'Merchant name',
    example: 'Updated Merchant Store',
    required: false
  })
  @IsString()
  name?: string;
}
