import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { MerchantService, CreateMerchantDto } from './merchant.service';
import { MerchantEntity } from './entities/merchant.entity';
import { ProductEntity } from '../product/entities/product.entity';
import { CategoryEntity } from '../category/entities/category.entity';
import { DiscountEntity } from '../discount/entities/discount.entity';

@Resolver(() => MerchantEntity)
export class MerchantResolver {
  constructor(private readonly merchantService: MerchantService) {}

  @Public()
  @Query(() => [MerchantEntity], { name: 'merchants' })
  async findAll(@Context() context?: any) {
    return this.merchantService.findAll();
  }

  @Public()
  @Query(() => MerchantEntity, { name: 'merchant' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Context() context?: any
  ) {
    if (!id) {
      throw new BadRequestException('id is required');
    }
    return this.merchantService.findOne(id);
  }

  @Mutation(() => MerchantEntity)
  async createMerchant(
    @Args('createMerchantInput') createMerchantDto: CreateMerchantDto,
    @Context() context: any
  ) {
    // For merchant creation, we might allow public access or require admin authentication
    // Adjust based on your business requirements
    if (!createMerchantDto.id) {
      throw new BadRequestException('id is required');
    }
    if (!createMerchantDto.name) {
      throw new BadRequestException('name is required');
    }

    return this.merchantService.create(createMerchantDto);
  }

  @Mutation(() => MerchantEntity)
  async updateMerchant(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateMerchantInput') updateMerchantDto: Partial<CreateMerchantDto>,
    @Context() context: any
  ) {
    // Require authentication for updates
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Only allow merchants to update their own data or require admin access
    const userMerchantId = context.req.user.merchantId;
    if (userMerchantId !== id) {
      throw new UnauthorizedException('You can only update your own merchant data');
    }

    if (!id) {
      throw new BadRequestException('id is required');
    }
    return this.merchantService.update(id, updateMerchantDto);
  }

  @Mutation(() => Boolean)
  async removeMerchant(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Require authentication for deletion
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // This should typically require admin access
    // For now, only allow merchants to delete their own data
    const userMerchantId = context.req.user.merchantId;
    if (userMerchantId !== id) {
      throw new UnauthorizedException('You can only delete your own merchant data');
    }

    if (!id) {
      throw new BadRequestException('id is required');
    }
    await this.merchantService.remove(id);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => [ProductEntity])
  async products(@Parent() merchant: MerchantEntity) {
    return merchant.products || [];
  }

  @ResolveField(() => [CategoryEntity])
  async categories(@Parent() merchant: MerchantEntity) {
    return merchant.categories || [];
  }

  @ResolveField(() => [DiscountEntity])
  async discounts(@Parent() merchant: MerchantEntity) {
    return merchant.discounts || [];
  }
}
