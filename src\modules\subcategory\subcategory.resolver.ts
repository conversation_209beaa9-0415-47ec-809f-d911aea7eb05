import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { SubCategoryService } from './subcategory.service';
import { SubCategoryEntity } from './entities/subcategory.entity';
import { CreateSubCategoryDto, UpdateSubCategoryDto } from './dto/subcategory.dto';
import { CategoryEntity } from '../category/entities/category.entity';
import { ProductEntity } from '../product/entities/product.entity';

@Resolver(() => SubCategoryEntity)
export class SubCategoryResolver {
  constructor(private readonly subCategoryService: SubCategoryService) {}

  @Public()
  @Query(() => [SubCategoryEntity], { name: 'subCategories' })
  async findAll(
    @Args('categoryId') categoryId: string,
    @Context() context?: any
  ) {
    if (!categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.subCategoryService.findAllByCategory(categoryId);
  }

  @Public()
  @Query(() => SubCategoryEntity, { name: 'subCategory' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string,
    @Context() context?: any
  ) {
    if (!categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.subCategoryService.findOne(id, categoryId);
  }

  @Mutation(() => SubCategoryEntity)
  async createSubCategory(
    @Args('createSubCategoryInput') createSubCategoryDto: CreateSubCategoryDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!createSubCategoryDto.categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    if (!createSubCategoryDto.name) {
      throw new BadRequestException('name is required');
    }

    return this.subCategoryService.create(createSubCategoryDto);
  }

  @Mutation(() => SubCategoryEntity)
  async updateSubCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string,
    @Args('updateSubCategoryInput') updateSubCategoryDto: UpdateSubCategoryDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    return this.subCategoryService.update(id, categoryId, updateSubCategoryDto);
  }

  @Mutation(() => Boolean)
  async removeSubCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('categoryId') categoryId: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!categoryId) {
      throw new BadRequestException('categoryId is required');
    }
    await this.subCategoryService.remove(id, categoryId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => CategoryEntity)
  async category(@Parent() subCategory: SubCategoryEntity) {
    return subCategory.category;
  }

  @ResolveField(() => [ProductEntity])
  async products(@Parent() subCategory: SubCategoryEntity) {
    return subCategory.products || [];
  }
}
