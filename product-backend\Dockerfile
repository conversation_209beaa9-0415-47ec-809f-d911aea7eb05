FROM node:20-slim as builder
# Install required dependencies for Prisma
# RUN apk add --no-cache openssl postgresql-client  # For Alpine-based images
RUN apt-get update && apt-get install -y openssl postgresql-client && rm -rf /var/lib/apt/lists/*  # For Debian-based images
USER node
WORKDIR /home/<USER>
COPY --chown=node:node package*.json ./
RUN npm install --legacy-peer-deps 
COPY --chown=node:node . .

RUN npm run build \
&& npm prune --omit=dev

FROM node:20-slim
# Install required dependencies for Prisma and healthcheck
# RUN apk add --no-cache openssl postgresql-client  # For Alpine-based images
RUN apt-get update && apt-get install -y openssl postgresql-client wget && rm -rf /var/lib/apt/lists/*  # For Debian-based images
WORKDIR /home/<USER>
COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/
COPY --from=builder --chown=node:node /home/<USER>/prisma ./prisma

EXPOSE 3070

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=120s --retries=3 \
    CMD wget -q --spider http://localhost:3070/api/v1/health || exit 1

# Copy and fix start.sh script
COPY start.sh ./start.sh
RUN chmod +x ./start.sh && \
    chown node:node ./start.sh && \
    # Convert Windows line endings to Unix
    sed -i 's/\r$//' ./start.sh

USER node

CMD ["./start.sh"]