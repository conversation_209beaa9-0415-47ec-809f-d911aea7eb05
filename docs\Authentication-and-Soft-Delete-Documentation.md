# Authentication and Soft Delete Implementation Guide

## Overview

This document provides comprehensive information about the authentication system and soft delete functionality implemented in the product-backend system.

## 🌐 Service URLs

The product-backend system is accessible at the following URLs:

- **Main API**: `http://ng-product-local.dev.dev1.ngnair.com:3070/api/v1/health`
- **GraphQL Playground**: `http://ng-product-local.dev.dev1.ngnair.com:3070/graphql`
- **Swagger Documentation**: `http://ng-product-local.dev.dev1.ngnair.com:3070/api`

## 🔐 Authentication System

### JWT Payload Structure

The `/auth/me` endpoint now returns a complete JWT payload with the following structure:

```json
{
  "iss": "https://ngnair.com",
  "sub": "b3cb6af2-9156-4cc3-b744-f4e55edff2d3",
  "email": "<EMAIL>",
  "aud": ["partner"],
  "exp": **********,
  "iat": **********,
  "jti": "unique-jwt-id",
  "sid": "session-id",
  "azp": "authorized-party",
  "ent_set": ["default"],
  "perm_v": 1,
  "amr": ["password"],
  "auth_time": **********
}
```

### Authentication Flow

1. **Token Encryption**: Access tokens are encrypted using AES-256-GCM encryption
2. **Cookie Storage**: Encrypted tokens are stored in HTTP-only cookies
3. **Decryption**: Backend decrypts tokens using Rails-compatible encryption
4. **JWT Verification**: Tokens are verified using JWKS (currently simplified for testing)
5. **Payload Extraction**: Complete JWT payload is returned to client

### Testing Authentication

Use the provided test script to verify authentication:

```bash
node test-auth.js
```

The script tests the `/auth/me` endpoint with the provided encrypted access token.

## 🗑️ Soft Delete Functionality

### Database Schema Changes

Added `deletedAt` timestamp fields to all models:

- ✅ **Product** (already existed)
- ✅ **Variant** (already existed) 
- ✅ **Discount** (already existed)
- ✅ **Category** (newly added)
- ✅ **SubCategory** (newly added)
- ✅ **Merchant** (newly added)
- ✅ **SubscriptionPlan** (newly added)
- ✅ **PaymentPlan** (newly added)
- ✅ **TransactionLog** (newly added)

### Base Soft Delete Service

Created `BaseSoftDeleteService` class providing common soft delete operations:

#### Core Methods:
- `softDelete(id, userId)` - Soft delete a record
- `restore(id)` - Restore a soft deleted record
- `permanentDelete(id)` - Permanently delete (hard delete)
- `findAllActive(where, include)` - Find non-deleted records
- `findAllDeleted(where, include)` - Find soft deleted records
- `findActiveById(id, include)` - Find single active record
- `countActive(where)` - Count active records
- `countDeleted(where)` - Count deleted records

### Implementation Status

#### ✅ Completed Modules:
1. **Category Module**
   - Extends `BaseSoftDeleteService`
   - Soft delete with subcategory cascade
   - New endpoints: `/deleted/list`, `/:id/restore`, `/:id/permanent`

2. **Product Module**
   - Extends `BaseSoftDeleteService`
   - Already filtered by `deletedAt: null` in queries
   - Updated `remove()` method to use soft delete

#### 🔄 Pending Modules:
- SubCategory
- Variant  
- Discount
- Merchant
- SubscriptionPlan
- PaymentPlan
- TransactionLog

### API Endpoints

#### Standard Endpoints (Updated):
- `DELETE /{resource}/{id}` - Now performs soft delete
- `GET /{resource}` - Excludes soft deleted records

#### New Soft Delete Endpoints:
- `GET /{resource}/deleted/list` - List soft deleted records
- `PATCH /{resource}/{id}/restore` - Restore soft deleted record
- `DELETE /{resource}/{id}/permanent` - Permanently delete record

### Example Usage

#### Category Soft Delete:
```bash
# Soft delete a category
DELETE /api/v1/categories/cat123?merchantId=merchant456

# List deleted categories
GET /api/v1/categories/deleted/list?merchantId=merchant456

# Restore a category
PATCH /api/v1/categories/cat123/restore?merchantId=merchant456

# Permanently delete
DELETE /api/v1/categories/cat123/permanent?merchantId=merchant456
```

## 🔧 Implementation Details

### Authentication Service Changes

1. **Updated Method**: `authenticateUserFromCookies()` now returns JWT payload
2. **Payload Validation**: `ensureCompletePayload()` ensures all required fields
3. **Error Handling**: Proper error responses for authentication failures

### Service Architecture

```
BaseSoftDeleteService (Abstract)
├── CategoryService extends BaseSoftDeleteService
├── ProductService extends BaseSoftDeleteService
└── [Other services to be updated]
```

### Database Migration

Applied migration: `20250915063745_add_soft_delete_fields`
- Added `deletedAt DateTime?` to all models
- Maintains backward compatibility

## 🚀 Next Steps

### Immediate Tasks:
1. **Complete Remaining Modules**: Update all remaining services to extend `BaseSoftDeleteService`
2. **GraphQL Integration**: Apply soft delete to all GraphQL resolvers
3. **Authentication Enhancement**: Extract `sub` from JWT for POST endpoints
4. **Documentation Update**: Complete API documentation with new endpoints

### Testing Recommendations:
1. Test soft delete functionality for each module
2. Verify cascade delete behavior
3. Test restore and permanent delete operations
4. Validate authentication with real JWT tokens

## 📋 Status Summary

- ✅ **Database Schema**: Updated with `deletedAt` fields
- ✅ **Base Service**: `BaseSoftDeleteService` implemented
- ✅ **Category Module**: Fully implemented with soft delete
- ✅ **Product Module**: Updated to use soft delete
- 🔄 **Authentication**: Working but needs format fix
- 🔄 **Remaining Modules**: Need soft delete implementation
- 🔄 **GraphQL**: Needs soft delete integration
- 🔄 **Documentation**: Needs completion

The foundation for authentication and soft delete functionality is now in place and ready for completion across all modules.
