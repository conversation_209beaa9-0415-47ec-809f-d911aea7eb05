import { Controller, Post, Get, Patch, Delete, Param, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { SubCategoryService } from './subcategory.service';
import { CreateSubCategoryDto, UpdateSubCategoryDto } from './dto/subcategory.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('SubCategories')
@Controller('categories/:categoryId/subcategories')
export class SubCategoryController {
  constructor(private readonly subCategoryService: SubCategoryService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new subcategory' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiBody({ type: CreateSubCategoryDto })
  @ApiResponse({ status: 201, description: 'Subcategory created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - subcategory name already exists' })
  async create(
    @Param('categoryId') categoryId: string,
    @Body() dto: CreateSubCategoryDto
  ) {
    // Check if subcategory name already exists for this category
    const existing = await this.subCategoryService.findAllByCategory(categoryId);
    if (existing.some(sub => sub.name.toLowerCase() === dto.name.toLowerCase())) {
      return {
        statusCode: 400,
        message: 'Subcategory name already exists for this category.',
        error: 'Bad Request'
      };
    }
    return this.subCategoryService.create({ ...dto, categoryId });
  }

  @Get()
  @ApiOperation({ summary: 'Get all subcategories for a category' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiResponse({ status: 200, description: 'Subcategories retrieved successfully' })
  async findAll(@Param('categoryId') categoryId: string) {
    return this.subCategoryService.findAllByCategory(categoryId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a subcategory by ID' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiParam({ name: 'id', description: 'Subcategory ID' })
  @ApiResponse({ status: 200, description: 'Subcategory retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Subcategory not found' })
  async findOne(
    @Param('categoryId') categoryId: string,
    @Param('id') id: string
  ) {
    return this.subCategoryService.findOne(id, categoryId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a subcategory' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiParam({ name: 'id', description: 'Subcategory ID' })
  @ApiBody({ type: UpdateSubCategoryDto })
  @ApiResponse({ status: 200, description: 'Subcategory updated successfully' })
  @ApiResponse({ status: 404, description: 'Subcategory not found' })
  async update(
    @Param('categoryId') categoryId: string,
    @Param('id') id: string,
    @Body() dto: UpdateSubCategoryDto
  ) {
    return this.subCategoryService.update(id, categoryId, dto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a subcategory' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiParam({ name: 'id', description: 'Subcategory ID' })
  @ApiResponse({ status: 204, description: 'Subcategory deleted successfully' })
  @ApiResponse({ status: 404, description: 'Subcategory not found' })
  async remove(
    @Param('categoryId') categoryId: string,
    @Param('id') id: string
  ) {
    await this.subCategoryService.remove(id, categoryId);
    return;
  }
}
