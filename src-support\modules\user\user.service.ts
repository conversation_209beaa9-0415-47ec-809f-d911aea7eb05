import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { GetUsersDto } from './dto/user.dto';
import { User, UserListResponse } from './user.model';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);
  private readonly authBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.authBaseUrl = 
      this.configService.get<string>('AUTH_SERVICE_URL') || 
      'https://ng-auth-dev.dev1.ngnair.com';
  }

  async getUsers(params: GetUsersDto, authToken: string): Promise<UserListResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.role) queryParams.append('role', params.role);
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.search) queryParams.append('search', params.search);

      const url = `${this.authBaseUrl}/admin/users?${queryParams.toString()}`;
      
      this.logger.log(`Fetching users from: ${url}`);

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Accept': '*/*'
          }
        })
      );

      const responseData = response.data;

      // Handle the external auth service response format: { success: true, data: [...], total: number }
      const usersArray = responseData.data || responseData.items || [];
      const total = responseData.total || usersArray.length;

      // Map the response to our User model
      const users: User[] = usersArray.map((item: any) => ({
        id: item.id,
        email: item.email,
        firstName: item.firstName || item.first_name,
        lastName: item.lastName || item.last_name,
        phone: item.phone,
        country: item.country,
        verifiedEmail: item.verifiedEmail || false,
        verifiedPhone: item.verifiedPhone || false,
        role: item.role || 'admin',
        createdAt: new Date(item.createdAt || item.created_at || new Date()),
        partnerId: item.partnerId || [],
        mfaEnabled: item.mfaEnabled || false,
        active: item.active !== undefined ? item.active : true,
        accountId: item.accountId
      }));

      return {
        items: users,
        total: total,
        page: params.page || 1,
        limit: params.limit || 10,
        pages: Math.ceil(total / (params.limit || 10))
      };

    } catch (error: any) {
      this.logger.error('Failed to fetch users from auth service', {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.response?.status === 401) {
        throw new BadRequestException('Invalid or expired authentication token');
      }

      if (error.response?.status === 403) {
        throw new BadRequestException('Insufficient permissions to access users');
      }

      throw new InternalServerErrorException('Failed to fetch users from auth service');
    }
  }

  async getUserById(userId: string, authToken: string): Promise<User> {
    try {
      const url = `${this.authBaseUrl}/admin/users/${userId}`;
      
      this.logger.log(`Fetching user by ID: ${url}`);

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Accept': '*/*'
          }
        })
      );

      const item = response.data;
      
      return {
        id: item.id,
        email: item.email,
        firstName: item.firstName,
        lastName: item.lastName,
        phone: item.phone,
        country: item.country,
        verifiedEmail: item.verifiedEmail,
        verifiedPhone: item.verifiedPhone,
        role: item.role,
        createdAt: new Date(item.createdAt),
        partnerId: item.partnerId || [],
        mfaEnabled: item.mfaEnabled,
        active: item.active,
        accountId: item.accountId
      };

    } catch (error: any) {
      this.logger.error('Failed to fetch user by ID from auth service', {
        userId,
        error: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.response?.status === 401) {
        throw new BadRequestException('Invalid or expired authentication token');
      }

      if (error.response?.status === 403) {
        throw new BadRequestException('Insufficient permissions to access user');
      }

      if (error.response?.status === 404) {
        throw new BadRequestException(`User with ID ${userId} not found`);
      }

      throw new InternalServerErrorException('Failed to fetch user from auth service');
    }
  }

  async getUsersByRole(role: string, authToken: string): Promise<User[]> {
    const response = await this.getUsers({ role, limit: 100 }, authToken);
    return response.items;
  }

  async getActiveUsers(authToken: string): Promise<User[]> {
    const response = await this.getUsers({ active: true, limit: 100 }, authToken);
    return response.items;
  }
}
