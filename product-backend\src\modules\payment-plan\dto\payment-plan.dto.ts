import { IsString, IsInt, IsOptional, IsBoolean, IsEnum } from 'class-validator';

export enum PaymentPlanStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum PaymentPlanFrequency {
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
}

export class CreatePaymentPlanDto {
  @IsString()
  productId!: string;

  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(PaymentPlanStatus)
  status?: PaymentPlanStatus = PaymentPlanStatus.ACTIVE;

  @IsEnum(PaymentPlanFrequency)
  frequency!: PaymentPlanFrequency;

  @IsInt()
  totalAmount!: number;

  @IsInt()
  installmentAmount!: number;

  @IsInt()
  totalInstallments!: number;

  @IsOptional()
  @IsInt()
  gracePeriodDays?: number;

  @IsOptional()
  @IsInt()
  lateFee?: number;

  @IsOptional()
  @IsBoolean()
  refundable?: boolean;

  @IsOptional()
  schedule?: any;
}

export class UpdatePaymentPlanDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(PaymentPlanStatus)
  status?: PaymentPlanStatus;

  @IsOptional()
  @IsEnum(PaymentPlanFrequency)
  frequency?: PaymentPlanFrequency;

  @IsOptional()
  @IsInt()
  totalAmount?: number;

  @IsOptional()
  @IsInt()
  installmentAmount?: number;

  @IsOptional()
  @IsInt()
  totalInstallments?: number;

  @IsOptional()
  @IsInt()
  gracePeriodDays?: number;

  @IsOptional()
  @IsInt()
  lateFee?: number;

  @IsOptional()
  @IsBoolean()
  refundable?: boolean;

  @IsOptional()
  schedule?: any;
}
