import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, ForbiddenException, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthService } from './auth.service';

// Decorators for role and permission-based access control
export const Roles = (...roles: string[]) => SetMetadata('roles', roles);
export const Permissions = (...permissions: string[]) => SetMetadata('permissions', permissions);

export const Public = () => {
  return (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('isPublic', true, target, propertyKey || '');
  };
};

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Get request object (handle both HTTP and GraphQL contexts)
    let request: any;

    try {
      // Try GraphQL context first
      const gqlContext = GqlExecutionContext.create(context);
      const gqlContextObj = gqlContext.getContext();
      request = gqlContextObj?.req || gqlContextObj?.request;
    } catch (error) {
      // Fall back to HTTP context
      request = context.switchToHttp().getRequest();
    }

    if (!request) {
      throw new UnauthorizedException('Request context not found');
    }

    try {
      // Extract cookies from request
      const cookies = request.cookies || {};

      // Use manual cookies if Fastify parsing failed
      let finalCookies = cookies;
      if (Object.keys(cookies).length === 0 && request.headers?.cookie) {
        const manualCookies: Record<string, string> = {};
        request.headers.cookie.split(';').forEach((cookie: string) => {
          const [name, value] = cookie.trim().split('=');
          if (name && value) {
            manualCookies[name] = decodeURIComponent(value);
          }
        });
        finalCookies = manualCookies;
      }

      // Authenticate user
      const user = await this.authService.authenticateFromCookies(finalCookies);

      // Attach user to request
      request.user = user;

      // Check role requirements
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const hasRole = requiredRoles.some(role => this.authService.hasRole(user, role));
        if (!hasRole) {
          throw new ForbiddenException(`Required role: ${requiredRoles.join(' or ')}`);
        }
      }

      // Check permission requirements
      const requiredPermissions = this.reflector.getAllAndOverride<string[]>('permissions', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasPermissions = this.authService.hasPermissions(user, requiredPermissions);
        if (!hasPermissions) {
          throw new ForbiddenException(`Required permissions: ${requiredPermissions.join(', ')}`);
        }
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new UnauthorizedException('Authentication required');
    }
  }
}
