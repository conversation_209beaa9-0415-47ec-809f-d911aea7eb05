import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { SubscriptionPlanService } from './subscription-plan.service';
import { SubscriptionPlanEntity } from './entities/subscription-plan.entity';
import { CreateSubscriptionPlanDto } from './dto/create-subscription-plan.dto';
import { UpdateSubscriptionPlanDto } from './dto/update-subscription-plan.dto';
import { ProductEntity } from '../product/entities/product.entity';

@Resolver(() => SubscriptionPlanEntity)
export class SubscriptionPlanResolver {
  constructor(private readonly subscriptionPlanService: SubscriptionPlanService) {}

  @Public()
  @Query(() => [SubscriptionPlanEntity], { name: 'subscriptionPlans' })
  async findAll(
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.subscriptionPlanService.findAllByProduct(productId);
  }

  @Public()
  @Query(() => SubscriptionPlanEntity, { name: 'subscriptionPlan' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context?: any
  ) {
    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.subscriptionPlanService.findOne(id, productId);
  }

  @Mutation(() => SubscriptionPlanEntity)
  async createSubscriptionPlan(
    @Args('createSubscriptionPlanInput') createSubscriptionPlanDto: CreateSubscriptionPlanDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!createSubscriptionPlanDto.productId) {
      throw new BadRequestException('productId is required');
    }
    if (!createSubscriptionPlanDto.name) {
      throw new BadRequestException('name is required');
    }
    if (!createSubscriptionPlanDto.duration) {
      throw new BadRequestException('duration is required');
    }
    if (createSubscriptionPlanDto.price === undefined || createSubscriptionPlanDto.price === null) {
      throw new BadRequestException('price is required');
    }

    return this.subscriptionPlanService.create(createSubscriptionPlanDto);
  }

  @Mutation(() => SubscriptionPlanEntity)
  async updateSubscriptionPlan(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Args('updateSubscriptionPlanInput') updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    return this.subscriptionPlanService.update(id, productId, updateSubscriptionPlanDto);
  }

  @Mutation(() => Boolean)
  async removeSubscriptionPlan(
    @Args('id', { type: () => ID }) id: string,
    @Args('productId') productId: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!productId) {
      throw new BadRequestException('productId is required');
    }
    await this.subscriptionPlanService.remove(id, productId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => ProductEntity)
  async product(@Parent() subscriptionPlan: SubscriptionPlanEntity) {
    return subscriptionPlan.product;
  }
}
