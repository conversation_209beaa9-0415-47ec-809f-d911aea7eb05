import { Modu<PERSON> } from "@nestjs/common";
import { PrismaModule } from "../../prisma/prisma.module";
import { TicketController } from "./ticket.controller";
import { TicketService } from "./ticket.service";
import { TicketResolver } from "./ticket.resolver";
import { AuthModule } from "../auth/src/auth.module";

@Module({
  imports: [PrismaModule, AuthModule],
  controllers: [TicketController],
  providers: [TicketService, TicketResolver],
  exports: [TicketService]
})
export class TicketModule {}
