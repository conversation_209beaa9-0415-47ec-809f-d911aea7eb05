import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateVariantDto, UpdateVariantDto } from './dto/variant.dto';
// import { TransactionLogService } from '../modules/transaction-log/transaction-log.service';

@Injectable()
export class VariantService {
  constructor(
    private readonly prisma: PrismaService,
    // private readonly transactionLogService: TransactionLogService,
  ) {}

  async create(dto: CreateVariantDto) {
    try {
      const created = await this.prisma.variant.create({ data: dto });
      // Fetch parent product to get merchantId
      const parentProduct = await this.prisma.product.findUnique({ where: { id: created.productId } });
      // await this.transactionLogService.createLog({
      //   action: 'CREATE',
      //   entity: 'Variant',
      //   entityId: created.id,
      //   userId: parentProduct?.merchantId,
      //   newValue: created,
      // });
      return created;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Variant SKU already exists for this product.');
      }
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to create variant'
      );
    }
  }

  async findAllByProduct(productId: string) {
    try {
      return await this.prisma.variant.findMany({ where: { productId } });
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to fetch variants'
      );
    }
  }

  async findOne(id: string, productId: string) {
    try {
      const variant = await this.prisma.variant.findUnique({ where: { id } });
      if (!variant) throw new NotFoundException('Variant not found');
      if (variant.productId !== productId) throw new BadRequestException('Product mismatch');
      return variant;
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to fetch variant'
      );
    }
  }

  async update(id: string, productId: string, dto: UpdateVariantDto) {
    try {
      const before = await this.prisma.variant.findUnique({ where: { id } });
      if (!before) throw new NotFoundException('Variant not found');
      if (before.productId !== productId) throw new BadRequestException('Product mismatch');
      const updated = await this.prisma.variant.update({ where: { id }, data: dto });
      // Fetch parent product to get merchantId
      const parentProduct = await this.prisma.product.findUnique({ where: { id: updated.productId } });
      // await this.transactionLogService.createLog({
      //   action: 'UPDATE',
      //   entity: 'Variant',
      //   entityId: updated.id,
      //   userId: parentProduct?.merchantId,
      //   oldValue: before,
      //   newValue: updated,
      // });
      return updated;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002'
      ) {
        throw new BadRequestException('Variant SKU already exists for this product.');
      }
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to update variant'
      );
    }
  }

  async remove(id: string, productId: string) {
    try {
      const before = await this.prisma.variant.findUnique({ where: { id } });
      if (!before) throw new NotFoundException('Variant not found');
      if (before.productId !== productId) throw new BadRequestException('Product mismatch');
      const deleted = await this.prisma.variant.delete({ where: { id } });
      // Fetch parent product to get merchantId
      const parentProduct = await this.prisma.product.findUnique({ where: { id: before.productId } });
      // await this.transactionLogService.createLog({
      //   action: 'DELETE',
      //   entity: 'Variant',
      //   entityId: deleted.id,
      //   userId: parentProduct?.merchantId,
      //   oldValue: before,
      // });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as any).message
          : 'Failed to delete variant'
      );
    }
  }
}