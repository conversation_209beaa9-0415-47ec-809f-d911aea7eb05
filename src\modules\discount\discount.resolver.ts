import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { DiscountService } from './discount.service';
import { DiscountEntity } from './entities/discount.entity';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { MerchantEntity } from '../merchant/entities/merchant.entity';
import { ProductEntity } from '../product/entities/product.entity';

@Resolver(() => DiscountEntity)
export class DiscountResolver {
  constructor(private readonly discountService: DiscountService) {}

  @Public()
  @Query(() => [DiscountEntity], { name: 'discounts' })
  async findAll(
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.discountService.findAllByMerchant(finalMerchantId);
  }

  @Public()
  @Query(() => DiscountEntity, { name: 'discount' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.discountService.findOne(id);
  }

  @Mutation(() => DiscountEntity)
  async createDiscount(
    @Args('createDiscountInput') createDiscountDto: CreateDiscountDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId || createDiscountDto.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    if (!createDiscountDto.name) {
      throw new BadRequestException('name is required');
    }
    if (!createDiscountDto.type) {
      throw new BadRequestException('type is required');
    }
    if (createDiscountDto.value === undefined || createDiscountDto.value === null) {
      throw new BadRequestException('value is required');
    }

    // Ensure the discount is created for the authenticated user's merchant
    const discountData = { ...createDiscountDto, merchantId };
    return this.discountService.create(discountData);
  }

  @Mutation(() => DiscountEntity)
  async updateDiscount(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateDiscountInput') updateDiscountDto: UpdateDiscountDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.discountService.update(id, merchantId, updateDiscountDto);
  }

  @Mutation(() => Boolean)
  async removeDiscount(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    await this.discountService.remove(id, merchantId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => MerchantEntity)
  async merchant(@Parent() discount: DiscountEntity) {
    return discount.merchant;
  }

  @ResolveField(() => [ProductEntity])
  async products(@Parent() discount: DiscountEntity) {
    return discount.products || [];
  }
}
