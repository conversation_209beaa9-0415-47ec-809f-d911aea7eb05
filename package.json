{"name": "support-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "generate:jwt": "ts-node generate-jwt.ts", "seed": "ts-node -r tsconfig-paths/register src/prisma/seed.ts", "dev:up": "docker compose -f docker-compose.dev.yml up --build", "dev:down": "docker compose -f docker-compose.dev.yml down -v", "dev:restart": "npm run dev:down && npm run dev:up"}, "dependencies": {"@aws-sdk/client-s3": "^3.837.0", "@aws-sdk/node-http-handler": "^3.370.0", "@aws-sdk/s3-request-presigner": "^3.837.0", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@as-integrations/fastify": "^2.0.0", "@fastify/cookie": "^10.0.1", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.10.0", "@types/graphql-upload": "^17.0.0", "@types/jsonwebtoken": "^9.0.10", "backblaze-b2": "^1.7.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "express": "^4.21.2", "fastify": "^4.29.1", "fastify-file-interceptor": "^1.0.9", "graphql": "^16.11.0", "graphql-upload": "^17.0.0", "graphql-upload-minimal": "^1.6.1", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "multer": "^2.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "postmark": "^4.0.5", "prisma": "^6.9.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.13", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}