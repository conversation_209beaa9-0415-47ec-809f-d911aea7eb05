import { Controller, Post, Get, Patch, Delete, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { PaymentPlanService } from './payment-plan.service';
import { CreatePaymentPlanDto, UpdatePaymentPlanDto } from './dto/payment-plan.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Payment Plans')
@Controller('products/:productId/payment-plan')
export class PaymentPlanController {
  constructor(private readonly paymentPlanService: PaymentPlanService) {}

  @Post()
  @ApiOperation({ summary: 'Create a payment plan for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiBody({ type: CreatePaymentPlanDto })
  @ApiResponse({ status: 201, description: 'Payment plan created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data' })
  create(@Param('productId') productId: string, @Body() dto: CreatePaymentPlanDto) {
    return this.paymentPlanService.create({ ...dto, productId });
  }

  @Get()
  @ApiOperation({ summary: 'Get payment plan for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Payment plan retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment plan not found' })
  findOne(@Param('productId') productId: string) {
    return this.paymentPlanService.findByProduct(productId);
  }

  @Patch()
  @ApiOperation({ summary: 'Update payment plan for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiBody({ type: UpdatePaymentPlanDto })
  @ApiResponse({ status: 200, description: 'Payment plan updated successfully' })
  @ApiResponse({ status: 404, description: 'Payment plan not found' })
  update(@Param('productId') productId: string, @Body() dto: UpdatePaymentPlanDto) {
    return this.paymentPlanService.update(productId, dto);
  }

  @Delete()
  @ApiOperation({ summary: 'Delete payment plan for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Payment plan deleted successfully' })
  @ApiResponse({ status: 404, description: 'Payment plan not found' })
  remove(@Param('productId') productId: string) {
    return this.paymentPlanService.remove(productId);
  }
}
