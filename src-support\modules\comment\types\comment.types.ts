import { Prisma } from "@prisma/client";

export const commentSelect = {
  id: true,
  message: true,
  authorId: true,
  ticketId: true,
  createdAt: true,
  updatedAt: true,
} as const;

export type CommentSelect = typeof commentSelect;

export type CommentPayload = Prisma.CommentGetPayload<{
  select: CommentSelect;
}>;

export interface CreateCommentInput {
  message: string;
  authorId: string;
  ticketId: string;
}

export interface FindManyCommentsArgs {
  where?: Prisma.CommentWhereInput;
  orderBy?: Prisma.CommentOrderByWithRelationInput;
  skip?: number;
  take?: number;
}
